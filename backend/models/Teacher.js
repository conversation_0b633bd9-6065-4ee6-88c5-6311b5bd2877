import { Schema, model } from 'mongoose';
import pkg from 'bcryptjs';
import { TaskSchema } from './Tasks.js';

const { hash, compare } = pkg;

const TeachingAnalyticsSchema = new Schema({
    // Teaching effectiveness metrics
    teachingEffectiveness: {
        averageStudentGrowth: { type: Number }, // Average proficiency growth across all students
        topicEffectiveness: [{
            topicId: { type: Schema.Types.ObjectId},
            growthRate: { type: Number }
            // comparedToSchoolAverage: { type: Number } // How this compares to other teachers

        }]
        // testQualityMetrics: {
        //     averageReliability: { type: Number },
        //     questionQuality: { type: Number }
        // }
    },
    
    // Cross-class insights
    crossClassInsights: {
        commonChallenges: [{
            topicId: { type: Schema.Types.ObjectId},
            frequencyScore: { type: Number }
        }],
        // successPatterns: [{
        //     approach: { type: String },
        //     effectivenessScore: { type: Number }
        // }],
        classComparison: [{
            classId: { type: Schema.Types.ObjectId, ref: 'Class' },
            relativeProficiency: { type: Number } // Compared to teacher's average
        }]
    },
    
    // Resource effectiveness
    // resourceEffectiveness: [{
    //     resourceId: { type: Schema.Types.ObjectId },
    //     resourceType: { type: String },
    //     effectivenessScore: { type: Number },
    //     usageFrequency: { type: Number },
    //     studentFeedback: { type: Number } // Average rating if collected
    // }],
    
    // Activity metrics
    activityMetrics: {
        testsCreated: { type: Number },
        feedbackProvided: { type: Number },
        interventionsInitiated: { type: Number },
        averageResponseTime: { type: Number } // Time to respond to student needs
    },
    
    lastUpdated: { type: Date, default: Date.now }
});

const TeacherSchema = new Schema({
    username: { type: String, required: true, unique: true, index: true },
    email: { type: String, required: true, unique: true, index: true },
    isEmailVerified: {
        type: Boolean,
        default: false
    },
    registeredAt: {
        type: Date,
        default: Date.now
    },
    password: { type: String, required: true },
    profileImage: {
        filename: { type: String, default: 'default.png' }, // Default profile image
        imageData: { type: Buffer }, // Buffer to store image data
        contentType: { type: String, default: 'image/png' } // Default content type
    },
    name: {
        first: { type: String },
        last: { type: String }
    },
    schoolCode: { type: Schema.Types.String, ref: 'School' },   // Reference to the School collection
    securityToken: { type: String },
    classes: [{ type: Schema.Types.ObjectId, ref: 'Class' }],                                     // Array of classes assigned to the teacher
    role: { type: String, default: 'Teacher' },
    testHistory: [{ type: Schema.Types.ObjectId, ref: 'TestHistory'}],                                       // Array of tests created by the teacher
    tasks: [TaskSchema],                                         // Array of tasks assigned to the teacher
    analytics: [TeachingAnalyticsSchema],                       // Array of teaching analytics
    isFirstLogin: { type: Boolean, default: true },
    // Credit System Fields
    credits: {
        balance: { type: Number, default: 10 }, // Free credits for new users
        totalEarned: { type: Number, default: 10 }, // Total credits ever earned (including free)
        totalSpent: { type: Number, default: 0 }, // Total credits ever spent
        lastUpdated: { type: Date, default: Date.now }
    },
    billing: {
        customerId: { type: String }, // Razorpay customer ID
        lastPurchaseDate: { type: Date },
        totalAmountSpent: { type: Number, default: 0 } // Total money spent in paisa (Razorpay uses paisa)
    }
}, { collection: 'teachers' });

// Hash password before saving a new or updated teacher record
TeacherSchema.pre('save', async function (next) {
    if (!this.isModified('password')) return next();
    this.password = await hash(this.password, 10);
    next();
});

// Method to compare passwords during login or authentication
TeacherSchema.methods.comparePassword = function (candidatePassword) {
    return compare(candidatePassword, this.password);
};

export default model('Teacher', TeacherSchema);
