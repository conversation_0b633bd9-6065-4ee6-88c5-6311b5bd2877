import express from 'express';
import { body } from 'express-validator';
import rateLimit from 'express-rate-limit';
import { verifyJWT } from '../middleware/verifyJWT.js';
import { getCreditInfo } from '../middleware/creditCheck.js';
import {
    getCreditPackages,
    getCreditBalance,
    createCreditOrder,
    verifyPayment,
    getTransactionHistory,
    getBillingAnalytics,
    getBillingDashboard,
    handleWebhook
} from '../controllers/creditController.js';

const router = express.Router();

// Rate limiting for webhook endpoint
const webhookRateLimit = rateLimit({
    windowMs: 1 * 60 * 1000, // 1 minute
    max: 100, // Limit each IP to 100 requests per windowMs
    message: 'Too many webhook requests from this IP',
    standardHeaders: true,
    legacyHeaders: false,
});

// Rate limiting for payment endpoints
const paymentRateLimit = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 10, // Limit each IP to 10 payment requests per windowMs
    message: 'Too many payment requests from this IP',
    standardHeaders: true,
    legacyHeaders: false,
});

/**
 * @route POST /api/credits/webhook
 * @desc Handle Razorpay webhooks (no auth required)
 * @access Public
 */
router.post('/webhook', webhookRateLimit, handleWebhook);

// Apply JWT verification to all other routes
router.use(verifyJWT);

/**
 * @route GET /api/credits/packages
 * @desc Get available credit packages
 * @access Private
 */
router.get('/packages', getCreditPackages);

/**
 * @route GET /api/credits/balance
 * @desc Get user's credit balance and statistics
 * @access Private
 */
router.get('/balance', getCreditBalance);

/**
 * @route POST /api/credits/order
 * @desc Create Razorpay order for credit purchase
 * @access Private
 */
router.post('/order', paymentRateLimit, [
    body('packageId')
        .isString()
        .notEmpty()
        .withMessage('Package ID is required')
        .isIn(['BASIC_10', 'STANDARD_50', 'PREMIUM_100'])
        .withMessage('Invalid package ID')
], createCreditOrder);

/**
 * @route POST /api/credits/verify
 * @desc Verify Razorpay payment and add credits
 * @access Private
 */
router.post('/verify', paymentRateLimit, [
    body('razorpay_order_id')
        .isString()
        .notEmpty()
        .withMessage('Razorpay order ID is required')
        .isLength({ min: 10, max: 50 })
        .withMessage('Invalid order ID format'),
    body('razorpay_payment_id')
        .isString()
        .notEmpty()
        .withMessage('Razorpay payment ID is required')
        .isLength({ min: 10, max: 50 })
        .withMessage('Invalid payment ID format'),
    body('razorpay_signature')
        .isString()
        .notEmpty()
        .withMessage('Razorpay signature is required')
        .isLength({ min: 50, max: 200 })
        .withMessage('Invalid signature format')
], verifyPayment);

/**
 * @route GET /api/credits/transactions
 * @desc Get user's transaction history with filtering
 * @access Private
 */
router.get('/transactions', getTransactionHistory);

/**
 * @route GET /api/credits/analytics
 * @desc Get user's billing analytics
 * @access Private
 */
router.get('/analytics', getBillingAnalytics);

/**
 * @route GET /api/credits/dashboard
 * @desc Get comprehensive billing dashboard data
 * @access Private
 */
router.get('/dashboard', getBillingDashboard);

/**
 * @route GET /api/credits/info
 * @desc Get user's credit info (for UI display)
 * @access Private
 */
router.get('/info', getCreditInfo, (req, res) => {
    try {
        res.json({
            success: true,
            data: {
                credits: req.user.credits,
                billing: req.user.billing
            }
        });
    } catch (error) {
        console.error('Error getting credit info:', error);
        res.status(500).json({
            success: false,
            message: 'Error fetching credit information'
        });
    }
});

export default router;
