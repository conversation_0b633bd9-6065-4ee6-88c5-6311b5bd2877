import { submitForGrading, getAllSubmissions, getPresignedUrl } from "../controllers/aegisGraderController.js";
import { verifyJWT } from "../middleware/verifyJWT.js";
import { checkAegisGraderCredits } from "../middleware/creditCheck.js";

import express from "express";

const router = express.Router();

// Apply JWT verification to all routes
router.use(verifyJWT);

// Apply credit check specifically to submission endpoint
router.post("/submit", checkAegisGraderCredits, submitForGrading);
router.get("/submissions/:userId", getAllSubmissions);

router.post("/getPresigned", getPresignedUrl);

export default router;
