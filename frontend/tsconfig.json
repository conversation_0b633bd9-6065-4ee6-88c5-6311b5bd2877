{
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"]  // Fixed path resolution to point to src directory
    },
    "target": "es5",
    "lib": [
      "dom",
      "dom.iterable",
      "esnext"
    ],
    "allowJs": true,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noFallthroughCasesInSwitch": true,
    "module": "esnext",
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "types": [
      "vite/client",          // Add Vite type definitions
      "vite-plugin-svgr/client" // Add SVGR type definitions
    ]
  },
  "include": [
    "src",
    "types",
    "vite.config.ts",        // Include Vite config file
    "**/*.d.ts"              // Include all type declaration files
  ],
  "exclude": ["node_modules"]
}