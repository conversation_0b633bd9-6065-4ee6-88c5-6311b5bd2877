import React, { useState, useEffect } from 'react';
import { XMarkIcon, CreditCardIcon, CheckIcon, ArrowPathIcon } from '@heroicons/react/24/outline';
import { useAxiosPrivate } from '@/hooks/useAxiosPrivate';
import { toast } from 'react-toastify';
import { createPortal } from 'react-dom';
import { fetchWithCache } from '@/utils/cacheUtil';

interface CreditPackage {
    id: string;
    credits: number;
    amount: number;
    amountInRupees: number;
    name: string;
    description: string;
    pricePerCredit: number;
}

interface CreditPurchaseModalProps {
    isOpen: boolean;
    onClose: () => void;
    onSuccess?: (creditsAdded: number, newBalance: number) => void;
}

declare global {
    interface Window {
        Razorpay: any;
    }
}

const CreditPurchaseModal: React.FC<CreditPurchaseModalProps> = ({
    isOpen,
    onClose,
    onSuccess
}) => {
    const [packages, setPackages] = useState<CreditPackage[]>([]);
    const [selectedPackage, setSelectedPackage] = useState<string | null>(null);
    const [loading, setLoading] = useState(false);
    const [purchasing, setPurchasing] = useState(false);
    const axiosPrivate = useAxiosPrivate();

    useEffect(() => {
        if (isOpen) {
            fetchPackages();
        }
    }, [isOpen]);

    const fetchPackages = async () => {
        try {
            setLoading(true);
            const response = await fetchWithCache(axiosPrivate, '/api/credits/packages');
            setPackages(response.data);
            
            // Auto-select the middle package (STANDARD_50)
            if (response.data.length > 0) {
                setSelectedPackage(response.data[1]?.id || response.data[0].id);
            }
        } catch (error) {
            console.error('Error fetching packages:', error);
            toast.error('Failed to load credit packages');
        } finally {
            setLoading(false);
        }
    };

    const handlePurchase = async () => {
        if (!selectedPackage) {
            toast.error('Please select a package');
            return;
        }

        try {
            setPurchasing(true);

            // Create Razorpay order
            const orderResponse = await axiosPrivate.post('/api/credits/order', {
                packageId: selectedPackage
            });

            const { orderId, amount, currency, packageInfo } = orderResponse.data.data;

            // Initialize Razorpay payment
            const options = {
                key: import.meta.env.VITE_RAZORPAY_KEY_ID,
                amount: amount,
                currency: currency,
                name: 'AegisScholar',
                description: `Purchase ${packageInfo.name}`,
                order_id: orderId,
                handler: async (response: any) => {
                    try {
                        // Verify payment
                        const verifyResponse = await axiosPrivate.post('/api/credits/verify', {
                            razorpay_order_id: response.razorpay_order_id,
                            razorpay_payment_id: response.razorpay_payment_id,
                            razorpay_signature: response.razorpay_signature
                        });

                        const { creditsAdded, newBalance } = verifyResponse.data.data;
                        
                        toast.success(`Successfully purchased ${creditsAdded} credits!`);
                        
                        if (onSuccess) {
                            onSuccess(creditsAdded, newBalance);
                        }
                        
                        onClose();
                    } catch (error: any) {
                        console.error('Payment verification failed:', error);
                        toast.error(error.response?.data?.message || 'Payment verification failed');
                    }
                },
                prefill: {
                    name: 'AegisScholar User',
                    email: '',
                    contact: ''
                },
                theme: {
                    color: '#3B82F6'
                },
                modal: {
                    ondismiss: () => {
                        setPurchasing(false);
                    }
                }
            };

            const razorpay = new window.Razorpay(options);
            razorpay.open();

        } catch (error: any) {
            console.error('Error creating order:', error);
            toast.error(error.response?.data?.message || 'Failed to create payment order');
            setPurchasing(false);
        }
    };

    const selectedPkg = packages.find(pkg => pkg.id === selectedPackage);

    if (!isOpen) return null;

    return createPortal(
        <div className="fixed inset-0 backdrop-blur-sm flex items-center justify-center z-50 p-4">
            <div className="bg-card rounded-xl shadow-xl max-w-md w-full max-h-[90dvh] overflow-auto">
                {/* Header */}
                <div className="flex items-center justify-between p-6 border-b border-border">
                    <div className="flex items-center gap-3">
                        <div className="p-2 bg-primary/10 rounded-xl">
                            <CreditCardIcon className="w-6 h-6 text-primary" />
                        </div>
                        <div>
                            <h2 className="text-xl font-semibold text-foreground">Purchase Credits</h2>
                            <p className="text-sm text-muted-foreground">Choose a package to continue using AegisGrader</p>
                        </div>
                    </div>
                    <button
                        onClick={onClose}
                        className="p-2 hover:bg-muted rounded-xl transition-colors"
                        disabled={purchasing}
                    >
                        <XMarkIcon className="w-5 h-5 text-muted-foreground" />
                    </button>
                </div>

                {/* Content */}
                <div className="p-6">
                    {loading ? (
                        <div className="space-y-4">
                            {[1, 2, 3].map(i => (
                                <div key={i} className="border border-border rounded-lg p-4 animate-pulse">
                                    <div className="h-4 bg-muted rounded mb-2"></div>
                                    <div className="h-3 bg-muted rounded w-2/3"></div>
                                </div>
                            ))}
                        </div>
                    ) : (
                        <div className="space-y-3">
                            {packages.map((pkg) => (
                                <div
                                    key={pkg.id}
                                    className={`border rounded-lg p-4 cursor-pointer transition-all ${
                                        selectedPackage === pkg.id
                                            ? 'border-primary bg-primary/5'
                                            : 'border-border hover:border-primary/50'
                                    }`}
                                    onClick={() => setSelectedPackage(pkg.id)}
                                >
                                    <div className="flex items-center justify-between">
                                        <div className="flex-1">
                                            <div className="flex items-center gap-2 mb-1">
                                                <h3 className="font-semibold text-foreground">{pkg.name}</h3>
                                                {pkg.id === 'STANDARD_50' && (
                                                    <span className="px-2 py-1 bg-primary/10 text-primary text-xs rounded-full">
                                                        Popular
                                                    </span>
                                                )}
                                                {pkg.id === 'PREMIUM_100' && (
                                                    <span className="px-2 py-1 bg-green-500/10 text-green-600 dark:text-green-400 text-xs rounded-full">
                                                        Best Value
                                                    </span>
                                                )}
                                            </div>
                                            <p className="text-sm text-muted-foreground mb-2">{pkg.description}</p>
                                            <div className="flex items-center gap-4 text-sm">
                                                <span className="font-medium text-foreground">
                                                    ₹{pkg.amountInRupees}
                                                </span>
                                                <span className="text-muted-foreground">
                                                    ₹{(pkg.pricePerCredit / 100).toFixed(0)} per credit
                                                </span>
                                            </div>
                                        </div>
                                        <div className={`w-5 h-5 rounded-full border-2 flex items-center justify-center ${
                                            selectedPackage === pkg.id
                                                ? 'border-primary bg-primary'
                                                : 'border-muted-foreground'
                                        }`}>
                                            {selectedPackage === pkg.id && (
                                                <CheckIcon className="w-3 h-3 text-primary-foreground" />
                                            )}
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}

                    {/* Selected Package Summary */}
                    {selectedPkg && (
                        <div className="mt-6 p-4 bg-muted/50 rounded-lg">
                            <h4 className="font-medium text-foreground mb-2">Order Summary</h4>
                            <div className="flex justify-between items-center text-sm mb-1">
                                <span className="text-muted-foreground">Package:</span>
                                <span className="text-foreground">{selectedPkg.name}</span>
                            </div>
                            <div className="flex justify-between items-center text-sm mb-1">
                                <span className="text-muted-foreground">Credits:</span>
                                <span className="text-foreground">{selectedPkg.credits}</span>
                            </div>
                            <div className="flex justify-between items-center text-sm font-medium pt-2 border-t border-border">
                                <span className="text-foreground">Total:</span>
                                <span className="text-foreground">₹{selectedPkg.amountInRupees}</span>
                            </div>
                        </div>
                    )}
                </div>

                {/* Footer */}
                <div className="flex gap-3 p-6 border-t border-border">
                    <button
                        onClick={onClose}
                        className="flex-1 px-4 py-2 border border-border rounded-lg text-foreground hover:bg-muted transition-colors"
                        disabled={purchasing}
                    >
                        Cancel
                    </button>
                    <button
                        onClick={handlePurchase}
                        disabled={!selectedPackage || purchasing || loading}
                        className="flex-1 px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
                    >
                        {purchasing ? (
                            <>
                                <ArrowPathIcon className="w-4 h-4 animate-spin" />
                                Processing...
                            </>
                        ) : (
                            'Purchase Credits'
                        )}
                    </button>
                </div>
            </div>
        </div>,
        document.body
    );
};

export default CreditPurchaseModal;
