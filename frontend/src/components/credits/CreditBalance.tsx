import React, { useState, useEffect, useRef, useMemo } from 'react';
import { CreditCard, AlertTriangle, RefreshCw, Zap, CheckCircle, AlertCircle, ShoppingCart, Coins } from 'lucide-react';
import { useAxiosPrivate } from '@/hooks/useAxiosPrivate';
import { fetchWithCache } from '@/utils/cacheUtil';
import { createPortal } from 'react-dom';

interface CreditStats {
    currentBalance: number;
    totalEarned: number;
    totalSpent: number;
    totalPurchased: number;
    totalAmountSpent: number;
    lastUpdated: string;
}

interface CreditBalanceProps {
    onPurchaseClick?: () => void;
    className?: string;
}

const CreditBalance: React.FC<CreditBalanceProps> = ({
    onPurchaseClick,
    className = ""
}) => {
    const [creditStats, setCreditStats] = useState<CreditStats | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [showTooltip, setShowTooltip] = useState(false);
    const [tooltipPosition, setTooltipPosition] = useState({ top: 0, left: 0 });

    const axiosPrivate = useAxiosPrivate();
    const triggerRef = useRef<HTMLDivElement>(null);
    const timeoutRef = useRef<NodeJS.Timeout | null>(null);

    const isTouchDevice = useMemo(() => 'ontouchstart' in window || navigator.maxTouchPoints > 0, []);

    const fetchCreditStats = async () => {
        try {
            setLoading(true);
            setError(null);
            const responseData = await fetchWithCache(axiosPrivate, '/api/credits/balance');
            // fetchWithCache returns response.data directly, so we access .data from that
            setCreditStats(responseData.data);
        } catch (err: any) {
            console.error('Error fetching credit stats:', err);
            setError(err.response?.data?.message || 'Failed to fetch credit information');
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchCreditStats();
    }, []);

    useEffect(() => {
        return () => {
            if (timeoutRef.current) {
                clearTimeout(timeoutRef.current);
            }
        };
    }, []);

    const updateTooltipPosition = () => {
        if (triggerRef.current) {
            const rect = triggerRef.current.getBoundingClientRect();
            setTooltipPosition({
                top: rect.bottom + window.scrollY + 8,
                left: rect.right + window.scrollX - 200 // Adjust to align tooltip to the right
            });
        }
    };

    const handleMouseEnter = () => {
        if (timeoutRef.current) {
            clearTimeout(timeoutRef.current);
        }
        updateTooltipPosition();
        setShowTooltip(true);
    };

    const handleMouseLeave = () => {
        timeoutRef.current = setTimeout(() => {
            setShowTooltip(false);
        }, 150); // Delay to allow moving to tooltip
    };

    const handleTooltipMouseEnter = () => {
        if (timeoutRef.current) {
            clearTimeout(timeoutRef.current);
        }
    };

    const handleTooltipMouseLeave = () => {
        setShowTooltip(false);
    };

    // Handle outside clicks to close tooltip (useful for mobile)
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            const tooltipEl = document.querySelector('.credit-tooltip-portal');
            if (
                triggerRef.current &&
                !triggerRef.current.contains(event.target as Node) &&
                (!tooltipEl || !tooltipEl.contains(event.target as Node))
            ) {
                setShowTooltip(false);
            }
        };

        if (showTooltip) {
            document.addEventListener('click', handleClickOutside, true);
            return () => {
                document.removeEventListener('click', handleClickOutside, true);
            };
        }
    }, [showTooltip]);

    const isLowCredits = creditStats && creditStats.currentBalance <= 200;
    const isCriticalCredits = creditStats && creditStats.currentBalance <= 5;

    if (loading) {
        return (
            <div className={`inline-flex items-center gap-3 bg-muted/30 rounded-xl border border-border px-4 py-3 text-sm backdrop-blur-sm ${className}`}>
                <div className="w-4 h-4 bg-muted rounded-full animate-pulse"></div>
                <div className="h-4 bg-muted rounded animate-pulse w-20"></div>
            </div>
        );
    }

    if (error) {
        return (
            <div className={`inline-flex items-center gap-3 bg-destructive/10 rounded-xl border border-destructive/20 px-4 py-3 text-sm backdrop-blur-sm ${className}`}>
                <AlertTriangle className="w-4 h-4 text-destructive" />
                <span className="text-destructive font-medium">Connection Error</span>
                <button
                    onClick={fetchCreditStats}
                    className="text-destructive hover:text-destructive/80 flex items-center gap-1 transition-colors p-1 rounded-md hover:bg-destructive/10"
                >
                    <RefreshCw className="w-3 h-3" />
                </button>
            </div>
        );
    }

    if (!creditStats) {
        return null;
    }

    const getStatusConfig = () => {
        if (isCriticalCredits) {
            return {
                message: 'Urgent: Only a few credits remaining',
                variant: 'critical' as const
            };
        }

        if (isLowCredits) {
            return {
                message: 'Running low on credits',
                variant: 'warning' as const
            };
        }

        return {
            message: 'Credits available for AegisGrader',
            variant: 'normal' as const
        };
    };

    const statusConfig = getStatusConfig();

    const getVariantClasses = () => {
        switch (statusConfig.variant) {
            case 'critical':
                return 'bg-destructive/10 border-destructive/20 text-destructive hover:bg-destructive/15';
            case 'warning':
                return 'bg-muted/50 border-border text-foreground hover:bg-muted/70';
            default:
                return 'bg-muted/30 border-border text-foreground hover:bg-muted/50';
        }
    };

    return (
        <>
            <div
                ref={triggerRef}
                className={`relative inline-block ${className}`}
                onMouseEnter={handleMouseEnter}
                onMouseLeave={handleMouseLeave}
                onClick={
                    isTouchDevice
                        ? (e) => {
                              e.stopPropagation();
                              updateTooltipPosition();
                              setShowTooltip((prev) => !prev);
                          }
                        : undefined
                }
            >
                <div className={`inline-flex items-center gap-2 rounded-xl border p-2 text-sm font-medium transition-all duration-300 cursor-pointer backdrop-blur-sm ${getVariantClasses()}`}>
                    {/* <Coins className="w-4 h-4" /> */}
                    <img src="/money_filled.png" alt="Coin" className="w-6 h-6" />
                    {/* <img src="/money.png" alt="Coin" className="w-5 h-5" /> */}

                    <div className="flex items-center gap-2">
                        <span className="font-bold text-lg">{creditStats.currentBalance}</span>
                        {/* <span className="text-xs opacity-70 font-normal">Credits</span> */}
                    </div>
                </div>
            </div>

            {/* Portal-based Tooltip */}
            {showTooltip && createPortal(
                <div
                    className="fixed z-[9999] pointer-events-auto credit-tooltip-portal"
                    style={{
                        top: tooltipPosition.top,
                        left: tooltipPosition.left,
                    }}
                    onMouseEnter={handleTooltipMouseEnter}
                    onMouseLeave={handleTooltipMouseLeave}
                >
                    <div className="bg-popover text-popover-foreground rounded-lg border border-border p-4 shadow-xl backdrop-blur-sm min-w-max max-w-xs transition-all duration-200 ease-out transform scale-100 opacity-100">
                        <div className="space-y-3">
                            <div className="flex items-center">
                                <p className="font-semibold text-sm">
                                    {statusConfig.message}
                                </p>
                            </div>

                            <div className="space-y-2 text-xs text-muted-foreground">
                                <p className="flex items-center gap-2">
                                    Each test evaluation uses 1 credit
                                </p>
                            </div>

                            {(isLowCredits && onPurchaseClick) && (
                                <button
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        onPurchaseClick();
                                        setShowTooltip(false);
                                    }}
                                    className={`w-full px-3 py-2 rounded-lg text-xs font-medium transition-all duration-200 flex items-center justify-center gap-2 ${
                                        statusConfig.variant === 'critical'
                                            ? 'bg-destructive text-destructive-foreground hover:bg-destructive/90'
                                            : 'bg-primary text-primary-foreground hover:bg-primary/90'
                                    } shadow-md hover:shadow-lg active:scale-95`}
                                >
                                    <ShoppingCart className="w-3 h-3" />
                                    Purchase More Credits
                                </button>
                            )}
                        </div>

                        {/* Tooltip arrow */}
                        <div className="absolute -top-1 right-4 transform w-2 h-2 bg-popover border-l border-t border-border rotate-45"></div>
                    </div>
                </div>,
                document.body
            )}
        </>
    );
};

export default CreditBalance;
