import React, { useState, useEffect, useRef } from 'react';
import { useS3Utils } from '@/hooks/useS3Utils';

interface BlobPdfViewerProps {
    s3Key: string;
    title: string;
    className?: string;
    style?: React.CSSProperties;
}

const BlobPdfViewer: React.FC<BlobPdfViewerProps> = ({ s3Key, title, className, style }) => {
    const [blobUrl, setBlobUrl] = useState<string>('');
    const [loading, setLoading] = useState<boolean>(true);
    const [error, setError] = useState<string>('');
    const [progress, setProgress] = useState<number>(0);
    const { fetchPdfAsBlob } = useS3Utils();
    const abortControllerRef = useRef<AbortController | null>(null);

    useEffect(() => {
        const loadPdfAsBlob = async () => {
            if (!s3Key) {
                setLoading(false);
                return;
            }

            // Cleanup previous blob URL
            if (blobUrl) {
                URL.revokeObjectURL(blobUrl);
                setBlobUrl('');
            }

            // Abort previous request
            if (abortControllerRef.current) {
                abortControllerRef.current.abort();
            }

            abortControllerRef.current = new AbortController();

            try {
                setLoading(true);
                setError('');
                setProgress(0);

                console.log('Fetching PDF as blob for s3Key:', s3Key);

                // Use the new fetchPdfAsBlob function which handles authentication
                const blob = await fetchPdfAsBlob(s3Key);

                console.log('PDF blob received, size:', blob.size);

                // Create object URL from blob
                const objectUrl = URL.createObjectURL(blob);
                setBlobUrl(objectUrl);
                setProgress(100);
                console.log('PDF blob created successfully');

            } catch (err: any) {
                if (err.name === 'AbortError') {
                    console.log('PDF fetch aborted');
                    return;
                }
                console.error('Error loading PDF as blob:', err);
                setError('Failed to load PDF');
            } finally {
                setLoading(false);
                setProgress(0);
            }
        };

        loadPdfAsBlob();

        // Cleanup function
        return () => {
            if (abortControllerRef.current) {
                abortControllerRef.current.abort();
            }
            if (blobUrl) {
                URL.revokeObjectURL(blobUrl);
            }
        };
    }, [s3Key, fetchPdfAsBlob]);

    if (loading) {
        return (
            <div className={`flex items-center justify-center bg-muted/30 ${className}`} style={style}>
                <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
                    <p className="text-sm text-muted-foreground">Loading PDF...</p>
                    {progress > 0 && (
                        <div className="mt-2 w-32 mx-auto">
                            <div className="bg-secondary rounded-full h-2">
                                <div 
                                    className="bg-primary h-2 rounded-full transition-all duration-300"
                                    style={{ width: `${progress}%` }}
                                />
                            </div>
                            <p className="text-xs text-muted-foreground mt-1">{progress}%</p>
                        </div>
                    )}
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className={`flex items-center justify-center bg-muted/30 ${className}`} style={style}>
                <div className="text-center">
                    <p className="text-sm text-destructive mb-2">{error}</p>
                    <button 
                        onClick={() => window.location.reload()}
                        className="text-xs text-primary hover:underline"
                    >
                        Retry
                    </button>
                </div>
            </div>
        );
    }

    if (!blobUrl) {
        return (
            <div className={`flex items-center justify-center bg-muted/30 ${className}`} style={style}>
                <p className="text-sm text-muted-foreground">No PDF available</p>
            </div>
        );
    }

    return (
        <div className={className} style={style}>
            <iframe
                src={`${blobUrl}#toolbar=0&navpanes=0&scrollbar=0&view=FitH`}
                title={title}
                width="100%"
                height="100%"
                style={{ border: 'none' }}
            />
        </div>
    );
};

export default BlobPdfViewer;
