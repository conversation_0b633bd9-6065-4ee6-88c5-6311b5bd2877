import React, { useState } from 'react';
import { ArrowDownTrayIcon } from '@heroicons/react/24/outline';
import { useS3Utils } from '@/hooks/useS3Utils';
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';

interface DownloadStudentReportProps {
    submissionData: {
        studentName: string;
        rollNumber: string;
        totalMarks: number;
        maxMarks: number;
        percentage: number;
        pdfUrl?: string;
        detailedBreakdown: any;
    };
    breakdownContentRef: React.RefObject<HTMLDivElement>;
}

const DownloadStudentReport: React.FC<DownloadStudentReportProps> = ({ 
    submissionData, 
    breakdownContentRef 
}) => {
    const [isDownloading, setIsDownloading] = useState(false);
    const { fetchPdfAsBlob } = useS3Utils();

    const downloadReport = async () => {
        if (!breakdownContentRef.current) return;

        setIsDownloading(true);

        try {
            // Create a new PDF in landscape mode
            const pdf = new jsPDF('landscape', 'mm', 'a4');
            const pageWidth = pdf.internal.pageSize.getWidth();
            const pageHeight = pdf.internal.pageSize.getHeight();

            // Add header with student info
            pdf.setFontSize(16);
            pdf.setFont('helvetica', 'bold');
            pdf.text(`Student Report: ${submissionData.studentName}`, pageWidth / 2, 15, { align: 'center' });

            pdf.setFontSize(12);
            pdf.setFont('helvetica', 'normal');
            pdf.text(`Roll Number: ${submissionData.rollNumber}`, 20, 25);
            pdf.text(`Score: ${submissionData.totalMarks}/${submissionData.maxMarks} (${submissionData.percentage}%)`, pageWidth - 80, 25);

            // Draw a line separator
            pdf.line(20, 30, pageWidth - 20, 30);

            let currentY = 40;

            // If there's a PDF URL, add a note about the original answer sheet
            if (submissionData.pdfUrl) {
                pdf.setFontSize(14);
                pdf.setFont('helvetica', 'bold');
                pdf.text('Original Answer Sheet', 20, currentY);
                currentY += 10;

                pdf.setFontSize(10);
                pdf.setFont('helvetica', 'normal');
                pdf.text('(Original answer sheet is available in the platform alongside this breakdown)', 20, currentY);
                currentY += 15;
            }

            // Add breakdown content title
            pdf.setFontSize(14);
            pdf.setFont('helvetica', 'bold');
            pdf.text('Question Breakdown Analysis', 20, currentY);
            currentY += 15;

            // Capture the breakdown content as canvas
            const canvas = await html2canvas(breakdownContentRef.current, {
                scale: 1.5,
                useCORS: true,
                allowTaint: true,
                backgroundColor: '#ffffff',
                width: breakdownContentRef.current.scrollWidth,
                height: breakdownContentRef.current.scrollHeight
            });

            const imgData = canvas.toDataURL('image/png');
            const imgWidth = pageWidth - 40; // Full width with margins
            const imgHeight = (canvas.height * imgWidth) / canvas.width;

            // Check if we need multiple pages
            const availableHeight = pageHeight - currentY - 20;

            if (imgHeight <= availableHeight) {
                // Fits on one page
                pdf.addImage(imgData, 'PNG', 20, currentY, imgWidth, imgHeight);
            } else {
                // Split across multiple pages
                const pageBreakHeight = availableHeight;
                let remainingHeight = imgHeight;
                let sourceY = 0;

                while (remainingHeight > 0) {
                    const currentPageHeight = Math.min(pageBreakHeight, remainingHeight);
                    const sourceHeight = (currentPageHeight * canvas.height) / imgHeight;

                    // Create a temporary canvas for this page section
                    const tempCanvas = document.createElement('canvas');
                    tempCanvas.width = canvas.width;
                    tempCanvas.height = sourceHeight;
                    const tempCtx = tempCanvas.getContext('2d');

                    if (tempCtx) {
                        tempCtx.drawImage(canvas, 0, sourceY, canvas.width, sourceHeight, 0, 0, canvas.width, sourceHeight);
                        const tempImgData = tempCanvas.toDataURL('image/png');
                        pdf.addImage(tempImgData, 'PNG', 20, currentY, imgWidth, currentPageHeight);
                    }

                    remainingHeight -= currentPageHeight;
                    sourceY += sourceHeight;

                    if (remainingHeight > 0) {
                        pdf.addPage();
                        currentY = 20;
                    }
                }
            }

            // Save the PDF
            const fileName = `${submissionData.studentName.replace(/\s+/g, '_')}_Report_${new Date().toISOString().split('T')[0]}.pdf`;
            pdf.save(fileName);

        } catch (error) {
            console.error('Error generating report:', error);
            alert('Failed to generate report. Please try again.');
        } finally {
            setIsDownloading(false);
        }
    };

    return (
        <button
            onClick={downloadReport}
            disabled={isDownloading}
            className="flex items-center gap-1 lg:gap-2 px-2 lg:px-3 py-2 text-xs lg:text-sm font-medium text-foreground bg-muted hover:bg-accent border border-border rounded-md transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            title="Download Student Report"
        >
            <ArrowDownTrayIcon className="w-3 lg:w-4 h-3 lg:h-4" />
            <span className="hidden sm:inline">{isDownloading ? 'Generating...' : 'Download'}</span>
        </button>
    );
};

export default DownloadStudentReport;
