import React, { useState, useRef, useEffect, useCallback } from "react";
import axios from 'axios';
import { CameraIcon, DocumentPlusIcon, FolderPlusIcon, SparklesIcon, XMarkIcon, DocumentArrowUpIcon, TrashIcon, CheckCircleIcon as CheckCircleSolid, ExclamationTriangleIcon as ExclamationTriangleSolid, ArrowPathIcon, EyeIcon, DocumentTextIcon, ArrowRightOnRectangleIcon, ArrowLeftStartOnRectangleIcon } from "@heroicons/react/24/outline"; // Added/updated icons
import { jsPDF } from 'jspdf';
import { useAxiosPrivate } from "@/hooks/useAxiosPrivate";
import { useNavigate } from 'react-router-dom';

import { TestSubmission, ScannedPage, TestDocument, TestDetails, RubricDocument, GradingStatus } from "@/types/aegisGrader";
import AegisScholarLogoWithoutText from '../assets/AegisScholarLogoIcon';
import ThemeToggle from '@/components/ThemeToggle';
import { useUser } from '../contexts/userContext';
import OnboardingTour, { TourStep } from '@/components/OnboardingTour';
import CreditBalance from '@/components/credits/CreditBalance';
import CreditPurchaseModal from '@/components/credits/CreditPurchaseModal';
import { createPortal } from "react-dom";
import { toast, ToastContainer } from "react-toastify";
import { usePageRefresh } from "@/hooks/usePageRefresh";
import { useMobileInteractions, useMobileKeyboard } from "@/hooks/useMobileInteractions";
import PulsatingDots from "@/components/PulsatingDotsLoader";

// --- Constants ---
const MAX_FILE_SIZE = 20 * 1024 * 1024; // 20MB
const FILE_SIZE_ERROR = `File size exceeds ${MAX_FILE_SIZE / (1024 * 1024)}MB. Please upload a smaller file.`;
const FILE_READ_TIMEOUT = 30000; // 30 seconds

// --- Reusable Input Component ---
interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
    label?: string;
}
const Input: React.FC<InputProps> = ({ label, id, className, ...props }) => (
    <div>
        {label && <label htmlFor={id} className="block text-sm font-medium text-muted-foreground mb-1">{label}</label>}
        <input
            id={id}
            className={`flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 ${className}`}
            {...props}
        />
    </div>
);

// Tour steps for AegisGrader
const tourSteps: TourStep[] = [
    {
        target: '[data-tour="test-details"]',
        content: 'Start by entering your test details including class name, subject, and date. This information will be used to organize your grading submissions.',
        title: 'Step 1: Test Details',
        placement: 'bottom',
    },
    {
        target: '[data-tour="question-paper"]',
        content: 'Upload your question paper PDF here. This helps the AI understand the test structure and provides context for accurate grading.',
        title: 'Step 2: Question Paper',
        placement: 'bottom',
    },
    {
        target: '[data-tour="rubric"]',
        content: 'Upload your grading rubric to ensure consistent and accurate scoring. The AI will use this to grade student responses.',
        title: 'Step 3: Grading Rubric',
        placement: 'bottom',
    },
    {
        target: '[data-tour="answer-sheets"]',
        content: 'Upload student answer sheets by scanning with your camera, uploading PDFs, or dragging and dropping files. You can process multiple sheets at once.',
        title: 'Step 4: Answer Sheets',
        placement: 'top',
    },
    {
        target: '[data-tour="submit-grading"]',
        content: 'Once all documents are uploaded, click here to submit for AI grading. The process typically takes a few minutes depending on the number of sheets.',
        title: 'Step 5: Submit for Grading',
        placement: 'top',
    },
    {
        target: '[data-tour="view-submissions"]',
        content: 'After grading is complete, view all your submissions here. You can track progress and access detailed results for each test.',
        title: 'Step 6: View Results',
        placement: 'top',
    },
];

// --- Reusable File Input Label ---
interface FileInputLabelProps {
    htmlFor: string;
    icon: React.ElementType;
    text: string;
    uploadedFile?: string;
    isLoading?: boolean;
}
const FileInputLabel: React.FC<FileInputLabelProps> = ({ htmlFor, icon: Icon, text, uploadedFile, isLoading }) => (
    <label htmlFor={htmlFor} className={`flex flex-col items-center justify-center gap-2 p-3 sm:p-4 min-h-[120px] sm:min-h-[140px] border-2 border-dashed rounded-lg cursor-pointer transition-colors touch-manipulation ${uploadedFile ? 'border-primary/50 bg-primary/5 hover:border-primary/70' : 'border-border hover:border-primary/50 bg-background hover:bg-accent/10'}`}>
        {isLoading ? (
            <>
                <ArrowPathIcon className="w-5 h-5 sm:w-6 sm:h-6 text-muted-foreground animate-spin" />
                <span className="text-xs text-muted-foreground">Loading...</span>
            </>
        ) : uploadedFile ? (
            <>
                <CheckCircleSolid className="w-5 h-5 sm:w-6 sm:h-6 text-success" />
                <span className="text-xs text-center text-muted-foreground truncate w-full px-1" title={uploadedFile}>{uploadedFile}</span>
                <span className="text-[10px] sm:text-xs text-primary hover:underline">(Replace)</span>
            </>
        ) : (
            <>
                <Icon className="w-6 h-6 sm:w-8 sm:h-8 text-muted-foreground/70" />
                <span className="text-xs sm:text-sm text-muted-foreground text-center px-2">{text}</span>
            </>
        )}
    </label>
);

// --- Main Component ---
export const AegisGrader: React.FC = () => {
    const { user, setUser } = useUser();
    const [isScanning, setIsScanning] = useState(false);
    const [scannedPages, setScannedPages] = useState<ScannedPage[]>([]);
    const [studentName, setStudentName] = useState("");
    const [rollNumber, setRollNumber] = useState("");
    const [error, setError] = useState<string>("");
    const [cameraLoading, setCameraLoading] = useState(false);
    const [testDetails, setTestDetails] = useState<TestDetails>({
        className: '',
        subject: '',
        date: new Date().toISOString().split('T')[0]
    });
    const [testDocuments, setTestDocuments] = useState<TestDocument[]>([]);
    const [rubricDocuments, setRubricDocuments] = useState<RubricDocument[]>([]);
    const [selectedDocumentPreview, setSelectedDocumentPreview] = useState<string | null>(null);
    const [isUploading, setIsUploading] = useState<Record<string, boolean>>({});
    const [debugInfo, setDebugInfo] = useState<string[]>([]);

    const videoRef = useRef<HTMLVideoElement>(null);
    const streamRef = useRef<MediaStream | null>(null);
    const questionPaperInputRef = useRef<HTMLInputElement>(null);
    const rubricInputRef = useRef<HTMLInputElement>(null);
    const answerSheetInputRef = useRef<HTMLInputElement>(null);
    const axiosPrivate = useAxiosPrivate();

    const [showLogoutModal, setShowLogoutModal] = useState(false);
    const [showCreditPurchaseModal, setShowCreditPurchaseModal] = useState(false);
    const [creditBalance, setCreditBalance] = useState<number | null>(null);
    usePageRefresh();

    // Mobile interactions
    const { isMobile, handleButtonPress, responsiveClasses } = useMobileInteractions({
        enableSwipe: false, // Disable swipe for this page as it might interfere with camera
        preventScroll: false
    });
    const { keyboardVisible } = useMobileKeyboard();


    const navigate = useNavigate();

    useEffect(() => {
        return () => {
            streamRef.current?.getTracks().forEach(track => track.stop());
        };
    }, []);

    const addDebug = (message: string) => {
        console.error(`DEBUG: ${message}`);
        setDebugInfo(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
    };

    const startScanning = useCallback(async () => {
        setError("");
        setDebugInfo([]);
        setCameraLoading(true);
        setIsScanning(true);
        addDebug("Attempting to start scanning...");

        streamRef.current?.getTracks().forEach(track => track.stop());
        streamRef.current = null;
        if (videoRef.current) videoRef.current.srcObject = null;

        const constraints = { video: { facingMode: 'environment', width: { ideal: 1280 }, height: { ideal: 720 } } };

        try {
            addDebug("Requesting media devices...");
            const stream = await navigator.mediaDevices.getUserMedia(constraints);
            addDebug("Stream obtained.");
            if (!videoRef.current) {
                addDebug("Video ref is null after getting stream.");
                console.error("Video element not ready.");
                return;
            }

            videoRef.current.srcObject = stream;
            streamRef.current = stream;

            videoRef.current.onloadedmetadata = () => addDebug("Video metadata loaded");
            videoRef.current.onloadeddata = () => addDebug("Video data loaded");
            videoRef.current.onplaying = () => { addDebug("Video playing"); setCameraLoading(false); };
            videoRef.current.onwaiting = () => addDebug("Video is waiting");
            videoRef.current.onerror = (e) => addDebug(`Video error: ${videoRef.current?.error?.message || 'Unknown'}`);

            addDebug("Attempting to play video...");
            await videoRef.current.play();

        } catch (err) {
            addDebug(`Camera access error: ${(err as Error).message}`);
            console.error("Camera access error:", err);
            const errorMsg = `Camera error: ${(err as Error).message}. Ensure permissions are granted and camera is not in use.`;
            setError(errorMsg);
            toast.error(errorMsg);
            setIsScanning(false);
            setCameraLoading(false);
        }
    }, []);

    const capturePage = useCallback(() => {
        addDebug("Attempting capture...");
        if (videoRef.current?.readyState === 4) {
            const canvas = document.createElement("canvas");
            canvas.width = videoRef.current.videoWidth;
            canvas.height = videoRef.current.videoHeight;
            const ctx = canvas.getContext("2d");
            if (ctx) {
                ctx.drawImage(videoRef.current, 0, 0);
                const imageUrl = canvas.toDataURL("image/webp", 0.9);
                setScannedPages(prev => [...prev, { imageUrl, timestamp: Date.now() }]);
                addDebug(`Page captured. Total pages: ${scannedPages.length + 1}`);
            } else {
                addDebug("Failed to get 2D context from canvas.");
                setError("Failed to get canvas context for capture.");
            }
        } else {
            addDebug(`Video not ready for capture. State: ${videoRef.current?.readyState}`);
            setError("Video not ready. Please wait.");
        }
    }, [scannedPages.length]);

    const stopScanning = useCallback(() => {
        addDebug("Stopping scanning...");
        streamRef.current?.getTracks().forEach(track => track.stop());
        streamRef.current = null;
        setIsScanning(false);
        setCameraLoading(false);
        if (videoRef.current) {
            videoRef.current.srcObject = null;
            videoRef.current.removeAttribute('src');
            videoRef.current.load();
        }
        addDebug("Scanning stopped.");
    }, []);

    const addImageToPdf = useCallback((pdf: jsPDF, imageUrl: string, pageWidth: number, pageHeight: number): Promise<void> => {
        return new Promise((resolve, reject) => {
            const img = new Image();
            img.onload = () => {
                try {
                    const margin = 10;
                    const maxWidth = pageWidth - (margin * 2);
                    const maxHeight = pageHeight - (margin * 2);
                    const imgRatio = img.width / img.height;
                    let imgWidth = maxWidth;
                    let imgHeight = imgWidth / imgRatio;

                    if (imgHeight > maxHeight) {
                        imgHeight = maxHeight;
                        imgWidth = imgHeight * imgRatio;
                    }
                    const x = (pageWidth - imgWidth) / 2;
                    const y = (pageHeight - imgHeight) / 2;
                    pdf.addImage(imageUrl, 'WEBP', x, y, imgWidth, imgHeight);
                    resolve();
                } catch (err) { reject(err); }
            };
            img.onerror = (err) => { reject(new Error("Failed to load image for PDF")); };
            img.src = imageUrl;
        });
    }, []);

    const generatePdfFromScannedPages = useCallback(async (sName: string, rNum: string, pages: ScannedPage[]): Promise<{ filename: string, pdfData: string }> => {
        if (pages.length === 0) console.error("No scanned pages.");

        const pdf = new jsPDF('p', 'mm', 'a4');
        pdf.setProperties({ title: `${sName} - ${rNum} Test`, subject: 'Scanned Test', author: 'AegisGrader' });

        pdf.setFontSize(22); pdf.text('Scanned Test Document', 105, 30, { align: 'center' });
        pdf.setFontSize(16);
        pdf.text(`Student: ${sName}`, 105, 50, { align: 'center' });
        pdf.text(`Roll Number: ${rNum}`, 105, 60, { align: 'center' });
        pdf.text(`Date: ${new Date().toLocaleDateString()}`, 105, 70, { align: 'center' });
        pdf.text(`Total Pages: ${pages.length}`, 105, 80, { align: 'center' });

        const pageWidth = pdf.internal.pageSize.getWidth();
        const pageHeight = pdf.internal.pageSize.getHeight();

        for (let i = 0; i < pages.length; i++) {
            pdf.addPage();
            try {
                await addImageToPdf(pdf, pages[i].imageUrl, pageWidth, pageHeight);
                pdf.setFontSize(10);
                pdf.text(`Page ${i + 1} of ${pages.length}`, pageWidth / 2, pageHeight - 10, { align: 'center' });
            } catch (err) {
                console.error(`Error adding image ${i + 1} to PDF:`, err);
                pdf.setFontSize(12); pdf.setTextColor(255, 0, 0);
                pdf.text(`Error loading page ${i + 1}`, 20, 20);
                pdf.setTextColor(0, 0, 0);
            }
        }

        const pdfData = pdf.output('datauristring');
        const filename = `${sName.replace(/\s+/g, '_')}_${rNum}_${Date.now()}.pdf`;
        return { filename, pdfData };
    }, [addImageToPdf]);

    const finishScanning = useCallback(async () => {
        addDebug("Finish scanning button clicked.");
        if (!studentName || !rollNumber) {
            setError("Please enter Student Name and Roll Number before finishing.");
            return;
        }
        if (scannedPages.length === 0) {
            setError("No pages have been scanned.");
            return;
        }

        stopScanning();
        setError("");
        setIsUploading(prev => ({ ...prev, scannedPdf: true }));

        try {
            addDebug(`Generating PDF for ${studentName}, ${scannedPages.length} pages...`);
            const { filename, pdfData } = await generatePdfFromScannedPages(studentName, rollNumber, scannedPages);
            addDebug(`PDF generated: ${filename}`);

            // Convert pdfData string to File object for consistency
            const pdfBlob = await fetch(pdfData).then(r => r.blob());
            const pdfFile = new File([pdfBlob], filename, { type: 'application/pdf' });

            const newDoc: TestDocument = {
                id: `test-${Date.now()}`,
                studentName,
                rollNumber,
                pdfUrl: filename,
                pdfData: pdfFile,
                className: testDetails.className,
                timestamp: Date.now(),
                uploading: false
            };

            setTestDocuments(prev => [...prev, newDoc]);
            addDebug(`Document added to list: ${newDoc.id}`);
            toast.success(`PDF created successfully for ${studentName}! (${scannedPages.length} pages)`);
            resetForm();

        } catch (err) {
            addDebug(`Error generating PDF: ${(err as Error).message}`);
            console.error("Error generating or saving PDF:", err);
            const errorMsg = `Failed to create PDF: ${(err as Error).message}`;
            setError(errorMsg);
            toast.error(errorMsg);
        } finally {
            setIsUploading(prev => ({ ...prev, scannedPdf: false }));
        }
    }, [studentName, rollNumber, scannedPages, testDetails.className, generatePdfFromScannedPages, stopScanning]);

    const resetForm = () => {
        setStudentName("");
        setRollNumber("");
        setScannedPages([]);
        addDebug("Form reset.");
    };

    const readFileAsDataURL = useCallback((file: File): Promise<string> => {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            let timeoutId: ReturnType<typeof setTimeout> | null = null;
            const cleanup = () => { if (timeoutId) clearTimeout(timeoutId); reader.onload = reader.onerror = reader.onabort = reader.onprogress = null; };
            timeoutId = setTimeout(() => { cleanup(); reader.abort(); reject(new Error(`File reading timed out (${FILE_READ_TIMEOUT / 1000}s).`)); }, FILE_READ_TIMEOUT);
            reader.onload = () => { cleanup(); const result = reader.result as string; if (result && result.startsWith('data:')) { resolve(result); } else { reject(new Error("FileReader returned invalid result.")); } };
            reader.onerror = () => { cleanup(); reject(new Error(`FileReader error: ${reader.error?.message || 'Read error'}`)); };
            reader.onabort = () => { cleanup(); console.error("Read aborted"); };
            reader.readAsDataURL(file);
        });
    }, []);

    const handleRemoveAnswerSheet = useCallback((docId: string) => {
        setTestDocuments(prev => prev.filter(doc => doc.id !== docId));
    }, []);

    const handleDocumentUpload = useCallback((type: 'rubric' | 'questionPaper') => async (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        const inputRef = event.target;
        if (!file) return;

        if (file.size > MAX_FILE_SIZE) {
            setError(FILE_SIZE_ERROR);
            if (inputRef) inputRef.value = '';
            return;
        }
        setError("");
        const tempId = `${type}-${Date.now()}`;
        setIsUploading(prev => ({ ...prev, [tempId]: true }));

        try {
            // const pdfData = await readFileAsDataURL(file);
            const newDoc: RubricDocument = {
                type,
                pdfUrl: file.name,
                pdfData: file,
                timestamp: Date.now()
            };
            // Replace existing doc of the same type, or add new
            setRubricDocuments(prev => [...prev.filter(doc => doc.type !== type), newDoc]);
            toast.success(`${type === 'questionPaper' ? 'Question Paper' : 'Rubric'} uploaded successfully!`);
        } catch (err) {
            console.error(`Error reading ${type} file:`, err);
            const errorMsg = `Failed to read ${file.name}: ${(err as Error).message}`;
            setError(errorMsg);
            toast.error(errorMsg);
        } finally {
            setIsUploading(prev => ({ ...prev, [tempId]: false }));
            // Keep file selected in input visually until successful upload (optional based on UX pref)
            // if (inputRef) inputRef.value = '';
        }
    }, [readFileAsDataURL]);

    const handleAnswerSheetUpload = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        const inputRef = event.target;
        if (!file) return;

        if (!studentName || !rollNumber) {
            setError("Please enter student name and roll number first.");
            if (inputRef) inputRef.value = '';
            return;
        }

        if (file.size > MAX_FILE_SIZE) {
            setError(FILE_SIZE_ERROR);
            if (inputRef) inputRef.value = '';
            return;
        }

        setError("");
        const tempId = `answerSheet-${Date.now()}`;
        setIsUploading(prev => ({ ...prev, [tempId]: true }));

        const placeholderDoc: TestDocument = {
            id: tempId,
            studentName: studentName,
            rollNumber: rollNumber,
            pdfUrl: file.name,
            className: testDetails.className,
            timestamp: Date.now(),
            uploading: true,
        };
        setTestDocuments(prev => [...prev, placeholderDoc]);

        const currentStudent = studentName;
        const currentRoll = rollNumber;
        setStudentName("");
        setRollNumber("");

        try {
            // const pdfData = await readFileAsDataURL(file);
            setTestDocuments(prevDocs => prevDocs.map(doc =>
                doc.id === tempId
                    ? { ...doc, pdfData: file, uploading: false, error: undefined, studentName: currentStudent, rollNumber: currentRoll }
                    : doc
            ));
            toast.success(`Answer sheet for ${currentStudent} uploaded successfully!`);
        } catch (err) {
            console.error(`Error reading answer sheet:`, err);
            const errorMsg = `Failed to read ${file.name}: ${(err as Error).message}`;
            setError(errorMsg);
            toast.error(errorMsg);
            setTestDocuments(prevDocs => prevDocs.map(doc =>
                doc.id === tempId
                    ? { ...doc, uploading: false, error: errorMsg, studentName: currentStudent, rollNumber: currentRoll }
                    : doc
            ));
        } finally {
            setIsUploading(prev => ({ ...prev, [tempId]: false }));
            if (inputRef) inputRef.value = '';
        }
    }, [readFileAsDataURL, studentName, rollNumber, testDetails.className]);

    // const handleSubmitForGrading = useCallback(async () => {
    //     const validAnswerSheets = testDocuments.filter(doc => doc.pdfData && !doc.error);
    //     if (validAnswerSheets.length === 0) {
    //         setError("No valid answer sheets ready for submission.");
    //         return;
    //     }
    //     if (!testDetails.className || !testDetails.subject) {
    //         setError("Please fill in Class Name and Subject.");
    //         return;
    //     }
    //
    //     const questionPaper = rubricDocuments.find(doc => doc.type === 'questionPaper');
    //     const rubric = rubricDocuments.find(doc => doc.type === 'rubric');
    //     
    //     console.log(`[Mehul] [DEBUG] got rubric: ${JSON.stringify(rubric)}, got questionPaper: ${JSON.stringify(questionPaper)}`);
    //
    //     const submissionPayload: TestSubmission = {
    //         testDetails,
    //         answerSheets: validAnswerSheets,
    //         questionPaper,
    //         rubric,
    //         status: GradingStatus.PENDING,
    //         gradingProgress: 0
    //     };
    //
    //     setError("");
    //     const submissionId = `local-${Date.now()}`; // Use a temporary local ID if needed for UI updates
    //     setIsUploading(prev => ({ ...prev, submission: true }));
    //
    //     try {
    //         setTestHistory(prev => [{ ...submissionPayload, id: submissionId }, ...prev]); // Add with local ID
    //
    //         // --- API Call ---
    //         // Simulate API delay for UI feedback
    //         await new Promise(resolve => setTimeout(resolve, 1500));
    //         const response = await axiosPrivate.post('/api/aegisGrader/submit', submissionPayload);
    //
    //         // Update history item status by its local ID
    //         setTestHistory(prev => prev.map(item => item.id === submissionId ? { ...item, status: GradingStatus.IN_PROGRESS } : item));
    //
    //         setTestDocuments([]);
    //         setRubricDocuments([]);
    //         setTestDetails({ className: '', subject: '', date: new Date().toISOString().split('T')[0] });
    //         if (questionPaperInputRef.current) questionPaperInputRef.current.value = ''; // Clear file inputs
    //         if (rubricInputRef.current) rubricInputRef.current.value = '';
    //
    //     } catch (error) {
    //         console.error("Error submitting for grading:", error);
    //         const errorMsg = `Submission failed: ${error instanceof Error ? error.message : String(error)}`;
    //         setError(errorMsg);
    //         setTestHistory(prev => prev.map(item => item.id === submissionId ? { ...item, status: GradingStatus.FAILED } : item));
    //     } finally {
    //         setIsUploading(prev => ({ ...prev, submission: false }));
    //     }
    // }, [testDocuments, rubricDocuments, testDetails]);

    const handleSubmitForGrading = useCallback(async () => {
        const validAnswerSheets = testDocuments.filter(doc => doc.pdfData && !doc.error);
        if (validAnswerSheets.length === 0) {
            toast.error("No valid answer sheets ready for submission.");
            setError("No valid answer sheets ready for submission.");
            return;
        }
        if (!testDetails.className || !testDetails.subject) {
            toast.error("Please fill in Class Name and Subject.");
            setError("Please fill in Class Name and Subject.");
            return;
        }

        const questionPaper = rubricDocuments.find(doc => doc.type === 'questionPaper');
        const rubric = rubricDocuments.find(doc => doc.type === 'rubric');

        console.log(`[Mehul] [DEBUG] got rubric: ${JSON.stringify(rubric)}, got questionPaper: ${JSON.stringify(questionPaper)}`);
        console.log(`[Mehul] [DEBUG] got answer sheet names: ${validAnswerSheets.map(doc => doc.pdfUrl)}`);

        let payload: any = {};
        let loadingToast = null;
        try {
              if (questionPaper || rubric) {
                loadingToast = toast.loading("Preparing submission for grading...", { autoClose: false });
                payload = {
                  questionPaperFile: questionPaper?.pdfUrl,
                  rubricFile: rubric?.pdfUrl,
                  answerSheets: validAnswerSheets.map(doc => {
                  return {filename: doc.pdfUrl, studentName: doc.studentName, rollNumber: doc.rollNumber};
                  }),
                  fileType: "application/pdf",
                  testDetails: {
                    className: testDetails.className,
                    subject: testDetails.subject,
                    CreatedBy: user?.id || 'unknown',
                    date: testDetails.date
                  }
                }
              } else {
                throw new Error("Both question paper and rubric must be uploaded before submission.");
              }
              const {data} = await axiosPrivate.post('/api/aegisGrader/getPresigned', payload);
              const { message, uploadUrlQuestion, uploadUrlRubric, uploadUrlSheet, fileNames, manifest, uploadUrlManifest }  = data;
              console.log(`[Mehul] [DEBUG] message: ${message}, Presigned URL response: ${uploadUrlQuestion}, url rubric: ${uploadUrlRubric}, filename: ${fileNames}`);
              console.log(`[Mehul][DEBUG] got sheet urls: ${JSON.stringify(uploadUrlSheet)}`)
              if (questionPaper) {
                await axios.put(uploadUrlQuestion, questionPaper?.pdfData);
              }
              if (rubric) {
                await axios.put(uploadUrlRubric, rubric?.pdfData);
              }

              for (const sheetUrl of uploadUrlSheet) {
                  console.log(`[Mehul][DEBUG] Uploading sheet: ${sheetUrl.sheetName} to ${sheetUrl.uploadUrl}`);
                  const matchSheet = validAnswerSheets.find(doc => doc.pdfUrl === sheetUrl.sheetName);
                  if (matchSheet) {
                      await axios.put(sheetUrl.uploadUrl, matchSheet.pdfData);
                  } else {
                      console.error(`Answer sheet with name: ${sheetUrl.sheetName} not found in valid answer sheets`);
                  }
              }

              // now write the manifest file
              console.log(`[Mehul][DEBUG] Got manifest presigned url: ${uploadUrlManifest}`);
              const manifestBlob = new Blob([JSON.stringify(manifest, null, 2)], { type: 'application/json' });

              await axios.put(uploadUrlManifest, manifestBlob, {
                  headers: {
                      'Content-Type': 'application/json'
                  }
              });

              if (loadingToast) {
                toast.update(loadingToast, {
                    render: `Successfully submitted ${validAnswerSheets.length} answer sheet${validAnswerSheets.length !== 1 ? 's' : ''} for grading!`,
                    type: 'success',
                    isLoading: false,
                    autoClose: 3000
                });
              }
              setTestDocuments([]);
              setRubricDocuments([]);
              setTestDetails({ className: '', subject: '', date: new Date().toISOString().split('T')[0] });
              if (questionPaperInputRef.current) questionPaperInputRef.current.value = ''; // Clear file inputs
              if (rubricInputRef.current) rubricInputRef.current.value = '';

            } catch (error) {
                console.error("Error during submission:", error);
                if (loadingToast) {
                  toast.update(loadingToast, {
                      render: `Submission failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
                      type: 'error',
                      isLoading: false,
                      autoClose: 5000
                  });
                }
            }
        }, [testDocuments, rubricDocuments, testDetails]);


    const getRubricDoc = (type: 'questionPaper' | 'rubric') => rubricDocuments.find(doc => doc.type === type);

    // --- Rendering ---
    return (
        <div className="min-h-screen w-full bg-background overflow-x-hidden p-2 pb-16">
            <ToastContainer
                position="top-right"
                autoClose={2000}
                hideProgressBar={true}
                newestOnTop={false}
                closeOnClick
                rtl={false}
                pauseOnFocusLoss
                draggable
                pauseOnHover
                theme={localStorage.getItem('theme') === 'dark' ? 'dark' : 'light'}
            />
            <div className="space-y-4 pt-2">
                {/* Header */}
                <header className="flex bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b border-border/40">
                    <div className="flex items-center min-w-0 flex-1 mb-2">
                        <AegisScholarLogoWithoutText
                            className="w-12 h-12 lg:hidden mr-2"
                            style={{ fill: 'var(--color-accent)' }}
                        />
                        <div className="min-w-0 flex-1">
                            <div className="flex items-center justify-between">
                                <h1 className="lg:text-3xl text-xl font-bold font-['Space_Grotesk'] text-foreground">AegisGrader</h1>
                                {/* Credit Balance Display */}
                                <CreditBalance
                                    onPurchaseClick={() => setShowCreditPurchaseModal(true)}
                                    className=""
                                />
                            </div>
                            <p className="text-xs md:text-sm text-muted-foreground line-clamp-2 md:line-clamp-1">
                                Welcome back, <span className="font-bold">{user?.firstName || user?.username}</span>!
                            </p>
                        </div>
                    </div>
                </header>

                {/* General Error Display Area */}
                {error && (
                    <div className="p-3 border border-destructive/50 bg-destructive/10 text-destructive rounded-md text-sm flex items-start gap-2" role="alert">
                        <ExclamationTriangleSolid className="h-5 w-5 flex-shrink-0 mt-0.5" />
                        <span className="flex-1 break-words">{error}</span>
                        <button onClick={() => setError('')} className="p-1 text-destructive/70 hover:text-destructive flex-shrink-0 flex items-center justify-center"><XMarkIcon className="h-4 w-4" /></button>
                    </div>
                )}

                {/* Main Content Grid */}
                <div className="grid grid-cols-1 xl:grid-cols-2 gap-4 sm:gap-6 lg:gap-8">
                    {/* Left Panel - Test Setup */}
                    <div className="space-y-4 sm:space-y-6">
                        {/* Test Details Card */}
                        <div className="bg-card rounded-xl shadow-md border border-border/50 p-4 sm:p-6" data-tour="test-details">
                            <h2 className="text-base sm:text-lg font-semibold mb-3 sm:mb-4 text-foreground border-b border-border/50 pb-2 sm:pb-3">1. Test Details</h2>
                            <div className="space-y-3 sm:space-y-4 pt-3 sm:pt-4">
                                <Input
                                    label="Class Name" id="class-name" placeholder="e.g., Grade 10 - Section A"
                                    value={testDetails.className}
                                    onChange={(e) => setTestDetails(prev => ({ ...prev, className: e.target.value }))}
                                    className="text-base"
                                />
                                <Input
                                    label="Subject" id="subject" placeholder="e.g., Mathematics Midterm"
                                    value={testDetails.subject}
                                    onChange={(e) => setTestDetails(prev => ({ ...prev, subject: e.target.value }))}
                                    className="text-base"
                                />
                                <Input
                                    label="Test Date" id="test-date" type="date"
                                    value={testDetails.date}
                                    onChange={(e) => setTestDetails(prev => ({ ...prev, date: e.target.value }))}
                                    className="text-base"
                                />
                            </div>
                        </div>

                        {/* Documents Upload Card - Corrected with Preview */}
                        <div className="bg-card rounded-xl shadow-md border border-border/50 p-4 sm:p-6" data-tour="question-paper">
                            <h2 className="text-base sm:text-lg font-semibold mb-3 sm:mb-4 text-foreground border-b border-border/50 pb-2 sm:pb-3">2. Question Paper & Rubric (PDF)</h2>
                            <div className="space-y-3 sm:space-y-4 pt-3 sm:pt-4">
                                {/* Upload Inputs Grid */}
                                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                                    <div>
                                        <input
                                            type="file" accept="application/pdf"
                                            onChange={handleDocumentUpload('questionPaper')}
                                            className="hidden" id="question-paper-upload"
                                            ref={questionPaperInputRef}
                                            disabled={!!isUploading[getRubricDoc('questionPaper')?.timestamp?.toString() ?? 'questionPaper']} // Disable during upload
                                        />
                                        <FileInputLabel
                                            htmlFor="question-paper-upload"
                                            icon={DocumentTextIcon}
                                            text="Upload Question Paper"
                                            uploadedFile={getRubricDoc('questionPaper')?.pdfUrl}
                                            isLoading={isUploading[getRubricDoc('questionPaper')?.timestamp?.toString() ?? 'questionPaper']}
                                        />
                                    </div>
                                    <div data-tour="rubric">
                                        <input
                                            type="file" accept="application/pdf"
                                            onChange={handleDocumentUpload('rubric')}
                                            className="hidden" id="rubric-upload"
                                            ref={rubricInputRef}
                                            disabled={!!isUploading[getRubricDoc('rubric')?.timestamp?.toString() ?? 'rubric']} // Disable during upload
                                        />
                                        <FileInputLabel
                                            htmlFor="rubric-upload"
                                            icon={DocumentTextIcon}
                                            text="Upload Rubric"
                                            uploadedFile={getRubricDoc('rubric')?.pdfUrl}
                                            isLoading={isUploading[getRubricDoc('rubric')?.timestamp?.toString() ?? 'rubric']}
                                        />
                                    </div>
                                </div>

                                {/* Uploaded Document Previews Section - ADDED BACK */}
                                {(getRubricDoc('questionPaper') || getRubricDoc('rubric')) && (
                                    <div className="space-y-2 pt-4 border-t border-border/50">
                                        <h3 className="text-sm font-medium text-muted-foreground">Uploaded Documents:</h3>
                                        {getRubricDoc('questionPaper') && (
                                            <div className="flex items-center justify-between gap-2 p-2 rounded-md bg-background/50 border border-border/50">
                                                <div className="flex items-center gap-2 min-w-0">
                                                    <DocumentTextIcon className="h-5 w-5 text-primary flex-shrink-0" />
                                                    <div className="flex-1 min-w-0">
                                                        <p className="text-sm font-medium text-foreground truncate">Question Paper</p>
                                                        <p className="text-xs text-muted-foreground truncate" title={getRubricDoc('questionPaper')?.pdfUrl}>{getRubricDoc('questionPaper')?.pdfUrl}</p>
                                                    </div>
                                                </div>
                                                <button
                                                    onClick={() => {
                                                        const doc = getRubricDoc('questionPaper');
                                                        if (doc?.pdfData instanceof File) {
                                                            const url = URL.createObjectURL(doc.pdfData);
                                                            setSelectedDocumentPreview(url);
                                                        }
                                                    }}
                                                    className="px-2 py-1 text-xs bg-secondary text-secondary-foreground rounded hover:bg-secondary/80 flex items-center gap-1 flex-shrink-0 min-h-[36px] touch-manipulation"
                                                    disabled={!getRubricDoc('questionPaper')?.pdfData}
                                                    title={getRubricDoc('questionPaper')?.pdfData ? "Preview Question Paper" : "Processing..."}
                                                >
                                                    <EyeIcon className="h-3.5 w-3.5" /> <span className="hidden sm:inline">Preview</span>
                                                </button>
                                            </div>
                                        )}
                                        {getRubricDoc('rubric') && (
                                            <div className="flex items-center justify-between gap-2 p-2 rounded-md bg-background/50 border border-border/50">
                                                <div className="flex items-center gap-2 min-w-0">
                                                    <DocumentTextIcon className="h-5 w-5 text-primary flex-shrink-0" />
                                                    <div className="flex-1 min-w-0">
                                                        <p className="text-sm font-medium text-foreground truncate">Rubric</p>
                                                        <p className="text-xs text-muted-foreground truncate" title={getRubricDoc('rubric')?.pdfUrl}>{getRubricDoc('rubric')?.pdfUrl}</p>
                                                    </div>
                                                </div>
                                                <button
                                                    onClick={() => {
                                                        const doc = getRubricDoc('rubric');
                                                        if (doc?.pdfData instanceof File) {
                                                            const url = URL.createObjectURL(doc.pdfData);
                                                            setSelectedDocumentPreview(url);
                                                        }
                                                    }}
                                                    className="px-2 py-1 text-xs bg-secondary text-secondary-foreground rounded hover:bg-secondary/80 flex items-center gap-1 flex-shrink-0 min-h-[36px] touch-manipulation"
                                                    disabled={!getRubricDoc('rubric')?.pdfData}
                                                    title={getRubricDoc('rubric')?.pdfData ? "Preview Rubric" : "Processing..."}
                                                >
                                                    <EyeIcon className="h-3.5 w-3.5" /> <span className="hidden sm:inline">Preview</span>
                                                </button>
                                            </div>
                                        )}
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>

                    {/* Right Panel - Answer Sheet Scanning / Upload */}
                    <div className="space-y-4 sm:space-y-6">
                        {/* Scanner Card */}
                        <div className="bg-card rounded-xl shadow-md border border-border/50 p-4 sm:p-6" data-tour="answer-sheets">
                            <h2 className="text-base sm:text-lg font-semibold mb-3 sm:mb-4 text-foreground border-b border-border/50 pb-2 sm:pb-3">3. Add Answer Sheets</h2>
                            <div className="space-y-3 sm:space-y-4 pt-3 sm:pt-4">
                                {/* Student Info (Required before Upload/Finish Scan) */}
                                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                                    <Input
                                        label="Student Name (for next sheet)" id="student-name" placeholder="Enter name" value={studentName}
                                        onChange={(e) => setStudentName(e.target.value)}
                                        disabled={isScanning}
                                        className="text-base"
                                    />
                                    <Input
                                        label="Roll Number (for next sheet)" id="roll-number" placeholder="Enter roll #" value={rollNumber}
                                        onChange={(e) => setRollNumber(e.target.value)}
                                        disabled={isScanning}
                                        className="text-base"
                                    />
                                </div>
                                {/* Camera View or Placeholder */}
                                {isScanning ? (
                                    <div className="relative w-full aspect-video bg-background rounded-lg overflow-hidden border border-border/50 min-h-[200px] sm:min-h-[250px]">
                                        <video ref={videoRef} autoPlay playsInline muted className="absolute inset-0 w-full h-full object-contain" style={{ display: cameraLoading ? 'none' : 'block' }} />
                                        {(cameraLoading || error.includes("Camera error")) && (
                                            <div className="absolute inset-0 flex flex-col items-center justify-center text-foreground bg-background/70 p-4 text-center">
                                                <p className="mb-2 text-sm sm:text-base">{error.includes("Camera error") ? error : "Initializing camera..."}</p>
                                                {cameraLoading && <PulsatingDots />}
                                            </div>
                                        )}

                                        {/* Scanning Controls */}
                                        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex gap-3">
                                            <button
                                                onClick={capturePage}
                                                className="px-4 py-2 text-sm bg-info text-info-foreground rounded-md hover:bg-info/90 flex items-center gap-2 transition-colors shadow-lg"
                                                disabled={cameraLoading || !streamRef.current}
                                            >
                                                <CameraIcon className="h-4 w-4" />
                                                <span className="hidden xs:inline">Capture Page</span>
                                                <span className="xs:hidden">Capture</span>
                                            </button>
                                            <button
                                                onClick={stopScanning}
                                                className="px-4 py-2 text-sm bg-destructive text-destructive-foreground rounded-md hover:bg-destructive/90 flex items-center gap-2 transition-colors shadow-lg"
                                            >
                                                <XMarkIcon className="h-4 w-4" />
                                                <span className="hidden xs:inline">Stop</span>
                                                <span className="xs:hidden">Stop</span>
                                            </button>
                                        </div>
                                    </div>
                                ) : (
                                    <div className="flex flex-col items-center justify-center gap-4 p-6 sm:p-8 border-2 border-dashed border-border rounded-lg bg-background/50 text-center min-h-[200px] sm:min-h-[250px]">
                                        {/* Interactive Icons */}
                                        <div className="flex items-center justify-center gap-8 sm:gap-12">
                                            {/* Camera Icon Button */}
                                            <button
                                                onClick={startScanning}
                                                disabled={cameraLoading}
                                                className="group flex flex-col items-center gap-2 p-4 rounded-lg hover:bg-muted/50 transition-all duration-200 cursor-pointer disabled:cursor-not-allowed disabled:opacity-50"
                                            >
                                                <div className="p-3 rounded-full bg-secondary/10 group-hover:bg-secondary/20 transition-colors">
                                                    <CameraIcon className="h-8 w-8 sm:h-10 sm:w-10 text-primary group-hover:text-primary/80" />
                                                </div>
                                                <span className="text-xs sm:text-sm font-medium text-foreground group-hover:text-primary transition-colors">
                                                    Scan with Camera
                                                </span>
                                            </button>

                                            {/* Divider */}
                                            <div className="h-12 w-px bg-border"></div>

                                            {/* Upload Icon Button */}
                                            <label
                                                htmlFor="answer-sheet-upload"
                                                className="group flex flex-col items-center gap-2 p-4 rounded-lg hover:bg-muted/50 transition-all duration-200 cursor-pointer"
                                            >
                                                <div className="p-3 rounded-full bg-secondary/10 group-hover:bg-secondary/20 transition-colors">
                                                    <DocumentArrowUpIcon className="h-8 w-8 sm:h-10 sm:w-10 text-secondary-foreground group-hover:text-secondary-foreground/80" />
                                                </div>
                                                <span className="text-xs sm:text-sm font-medium text-foreground group-hover:text-secondary-foreground transition-colors">
                                                    Upload PDF
                                                </span>
                                            </label>

                                            {/* Hidden file input */}
                                            <input
                                                type="file"
                                                accept="application/pdf"
                                                onChange={handleAnswerSheetUpload}
                                                className="hidden"
                                                id="answer-sheet-upload"
                                                ref={answerSheetInputRef}
                                                disabled={Object.values(isUploading).some(Boolean)}
                                            />
                                        </div>

                                        {/* Helper Text */}
                                        <p className="text-xs sm:text-sm text-muted-foreground px-4 max-w-md">
                                            {scannedPages.length > 0
                                                ? "Click 'Finish Scanning' below to save the scanned PDF."
                                                : "Choose your preferred method to get started"
                                            }
                                        </p>
                                    </div>
                                )}


                                {/* Scanned Page Thumbnails & Finish Button */}
                                {scannedPages.length > 0 && (
                                    <div className="space-y-3 pt-2">
                                        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2 sm:gap-0">
                                            <h3 className="text-sm font-medium text-muted-foreground">Scanned Pages ({scannedPages.length})</h3>
                                            <button onClick={finishScanning} className="px-3 py-2 text-sm bg-success text-success-foreground rounded-md hover:bg-success/90 flex items-center justify-center gap-1.5 transition-colors disabled:opacity-60 min-h-[44px] touch-manipulation" disabled={isUploading['scannedPdf'] || !studentName || !rollNumber}>
                                                {isUploading['scannedPdf'] ? <ArrowPathIcon className="h-4 w-4 animate-spin" /> : <CheckCircleSolid className="h-4 w-4" />}
                                                {isUploading['scannedPdf'] ? 'Saving...' : 'Finish Scanning'}
                                            </button>
                                        </div>
                                        <div className="grid grid-cols-2 xs:grid-cols-3 sm:grid-cols-4 md:grid-cols-5 gap-2 sm:gap-3">
                                            {scannedPages.map((page, index) => (
                                                <div key={page.timestamp} className="relative group">
                                                    <img src={page.imageUrl} alt={`Page ${index + 1}`} className="w-full aspect-[3/4] object-cover rounded-md border border-border/50 shadow-sm" />
                                                    <button
                                                        className="absolute top-1 right-1 p-2 sm:p-1.5 bg-card/80 backdrop-blur-sm rounded-full flex items-center justify-center shadow text-muted-foreground hover:bg-destructive hover:text-destructive-foreground transition-opacity border border-border/50 min-h-[32px] min-w-[32px]"
                                                        onClick={() => setScannedPages(prev => prev.filter(p => p.timestamp !== page.timestamp))}
                                                        aria-label={`Remove page ${index + 1}`}
                                                    >
                                                        <TrashIcon className="w-4 h-4 flex-shrink-0" />
                                                    </button>
                                                    <span className="absolute bottom-1 left-1 px-1.5 py-0.5 text-[10px] font-medium bg-black/50 text-white rounded">
                                                        {index + 1}
                                                    </span>
                                                </div>
                                            ))}
                                        </div>
                                        {(!studentName || !rollNumber) && <p className="text-xs text-warning text-center sm:text-left">Enter Student Name & Roll Number to save.</p>}
                                    </div>
                                )}
                            </div>
                        </div>

                        {/* Saved Answer Sheets Preview */}
                        <div className="bg-card rounded-xl shadow-md border border-border/50 p-4 sm:p-6">
                            <h2 className="text-base sm:text-lg font-semibold mb-3 sm:mb-4 text-foreground border-b border-border/50 pb-2 sm:pb-3">4. Ready Answer Sheets ({testDocuments.filter(d => d.pdfData && !d.error).length})</h2>
                            <div className="space-y-3 pt-3 sm:pt-4">
                                {testDocuments.length === 0 && (
                                    <p className="text-center text-sm text-muted-foreground py-6 sm:py-4">No answer sheets added yet.</p>
                                )}
                                {testDocuments.map((doc) => (
                                    <div key={doc.id} className={`flex items-center gap-3 sm:gap-4 p-3 sm:p-4 rounded-lg border ${doc.error ? 'border-destructive/40 bg-destructive/5' : doc.uploading ? 'border-info/40 bg-info/5 animate-pulse' : 'border-border/50 bg-background/50'}`}>
                                        <div className={`flex-shrink-0 w-8 h-8 sm:w-10 sm:h-10 rounded-full flex items-center justify-center text-xs sm:text-sm font-medium ${doc.error ? 'bg-destructive/20 text-destructive' : doc.uploading ? 'bg-info/20 text-info' : 'bg-primary/10 text-primary'}`}>
                                            {doc.studentName?.split(' ').map(n => n[0]).slice(0, 2).join('').toUpperCase() || '?'}
                                        </div>
                                        <div className="flex-1 min-w-0">
                                            <p className="text-sm sm:text-base font-medium text-foreground truncate">{doc.studentName} <span className="text-xs sm:text-sm text-muted-foreground">({doc.rollNumber})</span></p>
                                            <p className="text-xs sm:text-sm text-muted-foreground truncate" title={doc.pdfUrl}>{doc.pdfUrl}</p>
                                            {doc.uploading && <p className="text-xs text-info">Processing PDF...</p>}
                                            {doc.error && <p className="text-xs text-destructive truncate" title={doc.error}>Error: {doc.error.substring(0, 50)}...</p>}
                                        </div>
                                        <div className="flex gap-1.5 flex-shrink-0">
                                            {!doc.uploading && !doc.error && doc.pdfData && (
                                                <button onClick={() => {
                                                    if (doc.pdfData instanceof File) {
                                                        const url = URL.createObjectURL(doc.pdfData);
                                                        setSelectedDocumentPreview(url);
                                                    }
                                                }} className="p-2 sm:p-1.5 text-muted-foreground hover:text-primary hover:bg-primary/10 rounded-md transition-colors min-h-[44px] min-w-[44px] sm:min-h-auto sm:min-w-auto touch-manipulation" title="Preview PDF">
                                                    <EyeIcon className="h-4 w-4" />
                                                </button>
                                            )}
                                            <button onClick={() => handleRemoveAnswerSheet(doc.id)} className="p-2 sm:p-1.5 text-muted-foreground hover:text-destructive hover:bg-destructive/10 rounded-md transition-colors min-h-[44px] min-w-[44px] sm:min-h-auto sm:min-w-auto touch-manipulation" title="Remove Sheet">
                                                <TrashIcon className="h-4 w-4" />
                                            </button>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>

                        {/* Submit Button */}
                        <button
                            onClick={handleButtonPress(handleSubmitForGrading)}
                            disabled={testDocuments.filter(d => d.pdfData && !d.error).length === 0 || Object.values(isUploading).some(Boolean)}
                            className="w-full flex items-center justify-center gap-2 bg-gradient-to-r from-primary to-primary/80 text-primary-foreground px-4 sm:px-6 py-3 sm:py-4 rounded-lg shadow-md hover:shadow-lg hover:from-primary/90 hover:to-primary/70 transition-all duration-300 disabled:cursor-not-allowed disabled:shadow-none disabled:opacity-60 text-sm sm:text-base font-medium min-h-[52px] touch-manipulation mobile-button"
                            data-tour="submit-grading"
                        >
                            {isUploading['submission'] ? <ArrowPathIcon className="h-5 w-5 animate-spin" /> : <SparklesIcon className="h-5 w-5" />}
                            <span className="text-center">
                                {isUploading['submission'] ? 'Submitting...' :
                                    <>
                                        <span className="hidden sm:inline">
                                            Submit {testDocuments.filter(d => d.pdfData && !d.error).length} Sheet(s) for Grading
                                            ({testDocuments.filter(d => d.pdfData && !d.error).length} credit{testDocuments.filter(d => d.pdfData && !d.error).length !== 1 ? 's' : ''})
                                        </span>
                                        <span className="sm:hidden">
                                            Submit {testDocuments.filter(d => d.pdfData && !d.error).length} Sheet(s)
                                            ({testDocuments.filter(d => d.pdfData && !d.error).length} credit{testDocuments.filter(d => d.pdfData && !d.error).length !== 1 ? 's' : ''})
                                        </span>
                                    </>
                                }
                            </span>
                        </button>
                    </div>
                </div>

                {/* Document Preview Modal */}
                {selectedDocumentPreview && createPortal(
                    <div className="fixed inset-0 z-50 backdrop-blur-sm flex items-center justify-center p-2 sm:p-4 animate-fade-in">
                        <div className="bg-card border border-border rounded-xl shadow-2xl h-full w-full max-w-4xl flex flex-col max-h-[98vh] sm:max-h-[95vh]">
                            <div className="flex justify-between items-center p-3 sm:p-4 border-b border-border/50 flex-shrink-0">
                                <h3 className="text-base sm:text-lg font-semibold text-foreground">Document Preview</h3>
                                <button onClick={() => {
                                    setSelectedDocumentPreview(null);
                                    // Clean up object URL if it was created
                                    if (selectedDocumentPreview.startsWith('blob:')) {
                                        URL.revokeObjectURL(selectedDocumentPreview);
                                    }
                                }} className="p-2 sm:p-1.5 text-muted-foreground hover:bg-muted rounded-full transition-colors min-h-[44px] min-w-[44px] sm:min-h-auto sm:min-w-auto touch-manipulation" aria-label="Close preview">
                                    <XMarkIcon className="w-5 h-5" />
                                </button>
                            </div>
                            <div className="flex-grow p-2 overflow-hidden">
                                <iframe src={selectedDocumentPreview} className="w-full h-full rounded-md border border-border/50" title="Document Preview" onError={(e) => console.error("Preview iframe error:", e)} />
                            </div>
                        </div>
                    </div>,
                    document.body
                )}
                {/* Add fade-in animation style */}
                <style>{`
                    @keyframes fade-in { from { opacity: 0; } to { opacity: 1; } }
                    .animate-fade-in { animation: fade-in 0.2s ease-out forwards; }
                `}</style>
            </div>

            {/* Welcome Tour */}
            {user?.id &&
                (<OnboardingTour
                    steps={tourSteps}
                    user={user && user.id && typeof user.role === 'string'
                        ? { ...user, id: String(user.id), role: user.role }
                        : null}
                    tourKey="aegis-grader-tour"
                    onTourComplete={(completed, skipped) => {
                        console.log('Teacher tour completed:', { completed, skipped });
                    }}
                    startDelay={500}
                    continuous={true}
                    spotlightPadding={8}
                />)}

            {/* Credit Purchase Modal */}
            <CreditPurchaseModal
                isOpen={showCreditPurchaseModal}
                onClose={() => setShowCreditPurchaseModal(false)}
                onSuccess={(creditsAdded, newBalance) => {
                    setCreditBalance(newBalance);
                    toast.success(`Successfully purchased ${creditsAdded} credits! New balance: ${newBalance}`);
                }}
            />
        </div>
    );
};

export default AegisGrader;
