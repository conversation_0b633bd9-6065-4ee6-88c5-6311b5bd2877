import React, { useState, useRef, useEffect } from 'react';
import { register, googleRegister } from '../api';
import { useNavigate, useLocation } from 'react-router-dom';
import { 
  ArrowRightIcon, 
  UserIcon, 
  EnvelopeIcon, 
  LockClosedIcon, 
  ChevronDownIcon, 
  ArrowLeftIcon, 
  CheckBadgeIcon, 
  ShieldCheckIcon, 
  ArrowPathIcon, 
  EyeIcon, 
  EyeSlashIcon 
} from '@heroicons/react/24/outline';
import { SparklesIcon } from '@heroicons/react/24/solid';
import Background from '../components/Background';
import AegisScholarLogoWithoutText from '../assets/AegisScholarLogoIcon';
import { useAxiosPrivate } from '@/hooks/useAxiosPrivate';
import { motion } from 'framer-motion';
import { GoogleLogin } from '@react-oauth/google';
import { useUser } from '../contexts/userContext';
import { ToastContainer, toast } from 'react-toastify';

const Register: React.FC = () => {
    const location = useLocation();
    // Determine registration type from query param
    const searchParams = new URLSearchParams(location.search);
    const registrationType = searchParams.get('type'); // 'individual' or 'institution'
    const showSchoolCode = registrationType === 'institution';
    const isIndividualRegistration = registrationType === 'individual';
    const navigate = useNavigate();
    const { user, setUser } = useUser();
    const [userType, setUserType] = useState<'Student' | 'Teacher'>('Teacher');
    const [firstName, setFirstName] = useState('');
    const [lastName, setLastName] = useState('');
    const [username, setUsername] = useState('');
    const [email, setEmail] = useState('');
    const [password, setPassword] = useState('');
    const [schoolId, setSchoolId] = useState('');
    const [passwordType, setPasswordType] = useState('password');
    const [confirmPasswordType, setConfirmPasswordType] = useState('password');
    

    const [resendTimer, setResendTimer] = useState(30);
    const [canResend, setCanResend] = useState(false);
    const [error, setError] = useState('');
    const [isDropdownOpen, setIsDropdownOpen] = useState(false);
    const [verificationEmailSent, setVerificationEmailSent] = useState(false);
    const dropdownRef = useRef<HTMLDivElement>(null);
    const [registerLoader, setRegisterLoader] = useState(false);
    const axiosPrivate = useAxiosPrivate();

    const [confirmPassword, setConfirmPassword] = useState('');
    const [passwordsMatch, setPasswordsMatch] = useState(true);

    useEffect(() => {
        // Check if passwords match when either password or confirmPassword changes
        if (confirmPassword || password) {
            setPasswordsMatch(password === confirmPassword);
        }
    }, [password, confirmPassword]);

    useEffect(() => {
        let timer: ReturnType<typeof setInterval>;

        if (verificationEmailSent && resendTimer > 0) {
            timer = setInterval(() => {
                setResendTimer(prev => {
                    if (prev <= 1) {
                        setCanResend(true);
                        return 0;
                    }
                    return prev - 1;
                });
            }, 1000);
        }

        return () => clearInterval(timer);
    }, [verificationEmailSent, resendTimer]);

    useEffect(() => {
        // Close dropdown when clicking outside
        const handleClickOutside = (event: MouseEvent) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
                setIsDropdownOpen(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        // Validate passwords match before submitting
        if (password !== confirmPassword) {
            setPasswordsMatch(false);
            toast.error('Passwords do not match', { position: 'top-center', autoClose: 2000 });
            return;
        }

        try {
            const additionalData = userType === 'Student' ? { schoolId, firstName, lastName } : { schoolId, firstName, lastName };
            setRegisterLoader(true);
            const registerToast = toast.loading('Registering...', { position: 'top-center', toastId: 'register' });
            const response = await register(userType, username, email, password, additionalData);
            console.error('Registration response:', response);
            setVerificationEmailSent(!response.verified);
            if (response.verified) {
                toast.update(registerToast, {
                    render: 'Registration successful! Please log in.',
                    type: 'success',
                    isLoading: false,
                    autoClose: 2000,
                });
                // Redirect to appropriate login page based on registration type
                const loginPath = registrationType ? `/login?type=${registrationType}` : '/login';
                setTimeout(() => navigate(loginPath), 2000);
            } else {
                toast.update(registerToast, {
                    render: 'Verification email sent! Please check your inbox.',
                    type: 'info',
                    isLoading: false,
                    autoClose: 2000,
                });
            }
        } catch (err) {
            toast.error('Registration failed. Please try again.', { position: 'top-center', autoClose: 2000 });
            setError('Registration failed. Please try again.');
        } finally {
            setRegisterLoader(false);
        }
    };

    const handleResendEmail = async () => {
        try {
            setCanResend(false);
            setResendTimer(30);
            const resendToast = toast.loading('Resending verification email...', { position: 'top-center', toastId: 'resend' });
            await axiosPrivate.post('/api/auth/resend-verification-email', { email, userType });
            toast.update(resendToast, {
                render: 'Verification email resent!',
                type: 'success',
                isLoading: false,
                autoClose: 2000,
            });
        } catch (err) {
            toast.error('Failed to resend verification email', { position: 'top-center', autoClose: 2000 });
            setError('Failed to resend verification email');
        }
    };

    const togglePass = () => {
        if (passwordType === 'password') {
            setPasswordType('text');
        } else {
            setPasswordType('password');
        }
    }

    const toggleConfirmPass = () => {
        if (confirmPasswordType === 'password') {
            setConfirmPasswordType('text');
        } else {
            setConfirmPasswordType('password');
        }
    }

    // Google Sign-In success handler for new GIS
    const handleGoogleSuccess = async (credentialResponse: any) => {
        try {
            setRegisterLoader(true);
            const googleToast = toast.loading('Registering with Google...', { position: 'top-center', toastId: 'google-register' });
            const idToken = credentialResponse.credential;
            const res = await googleRegister(idToken, userType);
            if (res.verified) {
                sessionStorage.setItem('email', res.email);
                sessionStorage.setItem('role', userType);
                setUser({ ...user, accessToken: res.accessToken, email: res.email, role: userType });
                toast.update(googleToast, {
                    render: 'Google registration successful!',
                    type: 'success',
                    isLoading: false,
                    autoClose: 2000,
                });
                setTimeout(() => navigate('/aegis-ai', { replace: true }), 2000);
            } else {
                setVerificationEmailSent(true);
                toast.update(googleToast, {
                    render: 'Verification email sent! Please check your inbox.',
                    type: 'info',
                    isLoading: false,
                    autoClose: 2000,
                });
            }
        } catch (err) {
            toast.error('Google registration failed. Please try again.', { position: 'top-center', autoClose: 2000 });
            setError('Google registration failed. Please try again.');
        } finally {
            setRegisterLoader(false);
        }
    };

    const handleGoogleFailure = () => {
        toast.error('Google sign-in failed. Please try again.', { position: 'top-center', autoClose: 2000 });
        setError('Google sign-in failed. Please try again.');
    };

    return (
        <motion.div 
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ ease: 'easeIn', duration: 0.6 }}
            className="min-h-screen flex flex-col relative overflow-hidden bg-gradient-to-b from-background to-secondary/20"
        >
            <ToastContainer
                position="top-right"
                autoClose={2000}
                hideProgressBar={true}
                newestOnTop={false}
                closeOnClick
                rtl={false}
                pauseOnFocusLoss
                draggable
                pauseOnHover
                theme={localStorage.getItem('theme') === 'dark' ? 'dark' : 'light'}
            />
            {/* Main Content */}
            <div className="grow flex items-center justify-center p-4">
                <motion.div 
                    initial={{ y: 20, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ duration: 0.6, delay: 0.2 }}
                    className="backdrop-blur-sm bg-card p-8 rounded-2xl border border-border w-full max-w-4xl shadow-lg"
                >
                    <div className="flex flex-col items-center mb-4">
                        <AegisScholarLogoWithoutText 
                            className="w-16 h-16" 
                            style={{ fill: 'var(--color-accent)' }}
                        />
                    </div>

                    {verificationEmailSent ? (
                        <motion.div 
                            initial={{ opacity: 0, scale: 0.95 }}
                            animate={{ opacity: 1, scale: 1 }}
                            transition={{ duration: 0.4 }}
                            className="text-center my-6"
                        >
                            <h1 className="text-3xl font-bold text-primary mb-3">Verification Email Sent!</h1>

                            <div className="mx-auto bg-accent/10 rounded-full w-20 h-20 flex items-center justify-center mb-6">
                                <CheckBadgeIcon className="text-accent w-10 h-10" />
                            </div>

                            <div className="bg-accent/5 p-4 rounded-lg border border-accent/20 mb-6">
                                <p className="text-foreground">
                                    We've sent a verification link to <span className="font-semibold text-accent">{email}</span>
                                </p>
                            </div>
                            <div className="space-y-4">
                                <div className="flex items-center justify-center space-x-2 text-foreground/70">
                                    <EnvelopeIcon className="h-5 w-5" />
                                    <span>Check your inbox for the verification link</span>
                                </div>
                                <div className="flex items-center justify-center space-x-2 text-foreground/70">
                                    <ShieldCheckIcon className="h-5 w-5" />
                                    <span>Verify your email to complete registration</span>
                                </div>
                            </div>

                            <div className="mt-8 space-y-4">
                                <button
                                    onClick={handleResendEmail}
                                    disabled={!canResend}
                                    className={`w-full px-6 py-2 rounded-full flex items-center justify-center space-x-2 transition-all duration-300 ${canResend
                                            ? 'bg-accent text-white shadow-md shadow-accent/20 hover:shadow-lg hover:shadow-accent/30 hover:translate-y-[-2px]'
                                            : 'bg-muted text-foreground/50 cursor-not-allowed'
                                        }`}
                                >
                                    <ArrowPathIcon className="h-5 w-5" />
                                    <span>
                                        {canResend
                                            ? 'Resend Verification Email'
                                            : `Resend available in ${resendTimer}s`
                                        }
                                    </span>
                                </button>
                            </div>

                            <p className="text-xs text-foreground/60 mt-6">
                                If you continue to have problems, please contact <a href="mailto:<EMAIL>" className="text-accent hover:underline"><EMAIL></a>
                            </p>
                        </motion.div>
                    ) : (
                        <>
                            <div className="text-center mb-8 mt-2">
                                <h1 className="text-3xl font-bold text-primary font-['Space_Grotesk'] mb-1">
                                    Join AegisScholar
                                </h1>
                                <p className="text-foreground/70">Start your learning journey today</p>
                            </div>

                            {error && (
                                <motion.div 
                                    initial={{ opacity: 0, y: -10 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    className="mb-6 p-3 rounded-lg bg-red-50 text-red-600 text-center"
                                >
                                    {error}
                                </motion.div>
                            )}

                            {/* Temporarily commented out usertype selection - set to Teacher by default */}
                            {/* {!isIndividualRegistration && (
                                <div className="flex justify-center space-x-4 mb-6">
                                    <button
                                        type="button"
                                        onClick={() => setUserType('Student')}
                                        className={`px-6 py-2 rounded-full transition-all duration-300 cursor-pointer ${userType === 'Student'
                                            ? 'bg-accent text-white shadow-md shadow-accent/20'
                                            : 'bg-muted/50 text-foreground hover:bg-muted/70'
                                            }`}
                                    >
                                        Student
                                    </button>
                                    <button
                                        type="button"
                                        onClick={() => setUserType('Teacher')}
                                        className={`px-6 py-2 rounded-full transition-all duration-300 cursor-pointer ${userType === 'Teacher'
                                            ? 'bg-accent text-white shadow-md shadow-accent/20'
                                            : 'bg-muted/50 text-foreground hover:bg-muted/70'
                                            }`}
                                    >
                                        Teacher
                                    </button>
                                </div>
                            )} */}

                            <form onSubmit={handleSubmit} className="space-y-5">
                                {/* Name Fields - Side by Side */}
                                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                    <div>
                                        <label className="block text-sm font-medium text-foreground/90 mb-2">First Name*</label>
                                        <input
                                            type="text"
                                            required
                                            className="w-full px-4 py-3 bg-muted border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-accent/50 transition-all"
                                            placeholder="Enter your first name"
                                            value={firstName}
                                            onChange={(e) => setFirstName(e.target.value)}
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium text-foreground/90 mb-2">Last Name*</label>
                                        <input
                                            type="text"
                                            required
                                            className="w-full px-4 py-3 bg-muted border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-accent/50 transition-all"
                                            placeholder="Enter your last name"
                                            value={lastName}
                                            onChange={(e) => setLastName(e.target.value)}
                                        />
                                    </div>
                                </div>

                                {/* Username and Email - Side by Side */}
                                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                    <div>
                                        <label className="block text-sm font-medium text-foreground/90 mb-2">Username*</label>
                                        <div className="relative">
                                            <input
                                                type="text"
                                                required
                                                className="w-full px-4 py-3 pl-10 bg-muted border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-accent/50 transition-all"
                                                placeholder="Choose a username"
                                                value={username}
                                                onChange={(e) => setUsername(e.target.value)}
                                            />
                                            <UserIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-foreground/40 w-[18px] h-[18px]" />
                                        </div>
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium text-foreground/90 mb-2">Email*</label>
                                        <div className="relative">
                                            <input
                                                type="email"
                                                required
                                                className="w-full px-4 py-3 pl-10 bg-muted border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-accent/50 transition-all"
                                                placeholder="Enter your email"
                                                value={email}
                                                onChange={(e) => setEmail(e.target.value)}
                                            />
                                            <EnvelopeIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-foreground/40 w-[18px] h-[18px]" />
                                        </div>
                                    </div>
                                </div>

                                {/* Password Fields - Side by Side */}
                                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                    <div>
                                        <label className="block text-sm font-medium text-foreground/90 mb-2">
                                            Password*
                                        </label>
                                        <div className="relative">
                                            <input
                                                type={passwordType}
                                                required
                                                className="w-full px-4 py-3 pl-10 pr-10 bg-muted border border-border rounded-lg 
                                                focus:outline-none focus:ring-2 focus:ring-accent/50 transition-all"
                                                placeholder="Create a password"
                                                value={password}
                                                onChange={(e) => setPassword(e.target.value)}
                                            />
                                            <LockClosedIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-foreground/40 w-[18px] h-[18px]" />
                                            {(passwordType !== "password") ? 
                                                <EyeIcon className="absolute right-3 top-1/2 transform -translate-y-1/2 text-foreground/40 cursor-pointer w-[18px] h-[18px]" onClick={togglePass} /> : 
                                                <EyeSlashIcon className="absolute right-3 top-1/2 transform -translate-y-1/2 text-foreground/40 cursor-pointer w-[18px] h-[18px]" onClick={togglePass} />
                                            }
                                        </div>
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium text-foreground/90 mb-2">
                                            Confirm Password*
                                        </label>
                                        <div className="relative">
                                            <input
                                                type={confirmPasswordType}
                                                required
                                                className={`w-full px-4 py-3 pl-10 pr-10 bg-muted border ${confirmPassword && !passwordsMatch ? 'border-red-500' : 'border-border'
                                                    } rounded-lg focus:outline-none focus:ring-2 ${confirmPassword && !passwordsMatch ? 'focus:ring-red-500' : 'focus:ring-accent/50'
                                                    } transition-all`}
                                                placeholder="Re-enter your password"
                                                value={confirmPassword}
                                                onChange={(e) => setConfirmPassword(e.target.value)}
                                            />
                                            <LockClosedIcon className={`absolute left-3 top-1/2 transform -translate-y-1/2 ${confirmPassword && !passwordsMatch ? 'text-red-400' : 'text-foreground/40'} w-[18px] h-[18px]`} />
                                            {(confirmPasswordType !== "password") ? 
                                                <EyeIcon className="absolute right-3 top-1/2 transform -translate-y-1/2 text-foreground/40 cursor-pointer w-[18px] h-[18px]" onClick={toggleConfirmPass} /> : 
                                                <EyeSlashIcon className="absolute right-3 top-1/2 transform -translate-y-1/2 text-foreground/40 cursor-pointer w-[18px] h-[18px]" onClick={toggleConfirmPass} />
                                            }
                                        </div>
                                        {confirmPassword && !passwordsMatch && (
                                            <p className="text-red-500 text-xs mt-1">Passwords do not match</p>
                                        )}
                                    </div>
                                </div>

                                {/* School Code - Full Width */}
                                {showSchoolCode && (
                                    <div>
                                        <label className="block text-sm font-medium text-foreground/90 mb-2">
                                            School Code
                                        </label>
                                        <input
                                            type="text"
                                            className="w-full px-4 py-3 bg-muted border border-border rounded-lg 
                                            focus:outline-none focus:ring-2 focus:ring-accent/50 transition-all"
                                            placeholder="Enter your school ID"
                                            value={schoolId}
                                            onChange={(e) => setSchoolId(e.target.value)}
                                        />
                                    </div>
                                )}

                                {/* Terms of Service Agreement */}
                                <div className="text-center text-sm text-foreground/70 mb-4">
                                    By signing up, you agree to our{' '}
                                    <a
                                        href="/terms-of-service"
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="text-accent hover:underline"
                                    >
                                        Terms of Service
                                    </a>
                                    {' '}and{' '}
                                    <a
                                        href="/privacy-policy"
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="text-accent hover:underline"
                                    >
                                        Privacy Policy
                                    </a>
                                </div>

                                <button
                                    type="submit"
                                    className="w-full py-3 hover:cursor-pointer bg-accent text-white rounded-full hover:bg-accent/90 
                                    transition-all duration-300 flex items-center justify-center group shadow-md hover:shadow-lg hover:shadow-accent/20 hover:translate-y-[-2px] mt-2"
                                >
                                    {registerLoader ? (
                                        <ArrowPathIcon className="animate-spin mr-2 w-5 h-5" />
                                    ) : (
                                        <>
                                            Create Account
                                        </>
                                    )}
                                </button>
                            </form>

                            <div className="relative my-6">
                                <div className="absolute inset-0 flex items-center">
                                    <div className="w-full border-t border-border"></div>
                                </div>
                                <div className="relative flex justify-center">
                                    <span className="bg-card px-4 text-sm text-foreground/60">or</span>
                                </div>
                            </div>

                            {/* Google Sign-In Button */}
                            {isIndividualRegistration && (
                                <div className="flex justify-center mb-4">
                                    <GoogleLogin
                                        onSuccess={handleGoogleSuccess}
                                        onError={handleGoogleFailure}
                                        useOneTap
                                    />
                                </div>
                            )}

                            <p className="text-center text-foreground/80">
                                Already have an account?{' '}
                                <span
                                    onClick={() => navigate(registrationType ? `/login?type=${registrationType}` : '/login')}
                                    className="text-accent font-medium cursor-pointer hover:underline"
                                >
                                    Sign in
                                </span>
                            </p>
                        </>
                    )}
                </motion.div>
            </div>

            {/* Footer */}
            <footer className="py-4 text-center text-foreground/60 text-sm">
                <div className="container mx-auto px-6">
                    © 2025 AegisScholar. All rights reserved.
                </div>
            </footer>
        </motion.div>
    );
};

export default Register;