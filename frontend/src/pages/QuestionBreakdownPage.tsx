import React, { useEffect, useState } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { ArrowLeftIcon } from '@heroicons/react/24/outline';
import BlobPdfViewer from '@/components/BlobPdfViewer';
import QuestionBreakdownDisplay from '@/components/QuestionBreakdown';

interface QuestionBreakdownPageProps { }

const QuestionBreakdownPage: React.FC<QuestionBreakdownPageProps> = () => {
    const { submissionId } = useParams();
    const navigate = useNavigate();
    const location = useLocation();
    const [showDebugViewer, setShowDebugViewer] = useState(false);

    // Get the submission data from location state or fetch it
    const submissionData = location.state?.submissionData;

    useEffect(() => {
        // If no submission data is passed, we might need to fetch it
        if (!submissionData && submissionId) {
            // TODO: Fetch submission data by ID if needed
            console.log('Fetching submission data for ID:', submissionId);
        }
    }, [submissionId, submissionData]);

    if (!submissionData) {
        return (
            <div className="min-h-screen bg-background flex items-center justify-center">
                <div className="text-center">
                    <h2 className="text-xl font-semibold text-foreground mb-2">Loading...</h2>
                    <p className="text-muted-foreground">Fetching submission details</p>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-background">
            {/* Header */}
            <div className="border-b border-border py-4">
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                        <button
                            onClick={() => navigate(-1)}
                            className="flex items-center gap-2 p-2 rounded-md text-muted-foreground hover:bg-muted transition-all duration-200"
                        >
                            <ArrowLeftIcon className="w-4 h-4" />
                            <span className="text-sm font-medium">Back</span>
                        </button>
                        <div className="h-6 w-px bg-border" />
                        <h1 className="text-2xl font-semibold font-['Space_Grotesk'] text-foreground">Question Breakdown</h1>
                    </div>

                    <div className="flex items-center gap-6">
                        <div className="flex flex-col items-end">
                            <span className="text-md font-medium text-foreground">{submissionData.studentName}</span>
                            <span className="text-sm text-muted-foreground">Roll: {submissionData.rollNumber}</span>
                        </div>
                        <div className="h-8 w-px bg-border" />
                        <div className="flex flex-col items-end">
                            <span className="text-md font-semibold text-foreground">
                                {submissionData.totalMarks}/{submissionData.maxMarks}
                            </span>
                            <span className="text-sm text-muted-foreground">
                                {submissionData.percentage}% Score
                            </span>
                        </div>
                    </div>
                </div>

                {/* Debug Toggle - uncomment if needed */}
                {/* {submissionData.pdfUrl && (
        <button
            onClick={() => setShowDebugViewer(!showDebugViewer)}
            className="px-3 py-1.5 text-sm bg-muted text-muted-foreground rounded hover:bg-accent transition-colors"
        >
            {showDebugViewer ? 'Normal View' : 'Debug View'}
        </button>
    )} */}
            </div>


            {/* Main Content - Side by Side Layout */}
            <div className="flex h-[calc(100vh-80px)]">
                {/* PDF Viewer - Left Half */}
                {submissionData.pdfUrl && (
                    <div className="w-1/2 border-r border-border">
                        <div className="h-full p-1">
                            {/* <div className="flex items-center justify-between mb-3">
                                <h2 className="text-sm font-medium text-foreground">Answer Sheet</h2>
                            </div> */}
                            <div className="h-[calc(100%-2rem)] border border-border rounded-lg overflow-hidden">
                                <BlobPdfViewer
                                    s3Key={submissionData.pdfUrl || ''}
                                    title={`Answer sheet for ${submissionData.studentName}`}
                                    className="w-full h-full"
                                />
                            </div>
                        </div>
                    </div>
                )}

                {/* Question Analysis - Right Half */}
                <div className={`${submissionData.pdfUrl ? 'w-1/2' : 'w-full'} bg-background`}>
                    <div className="h-full p-1">
                        {/* <div className="flex items-center justify-between mb-3">
                            <h2 className="text-sm font-medium text-foreground">Question Analysis</h2>
                        </div> */}
                        <div className="h-[calc(100%-2rem)] overflow-y-auto">
                            <QuestionBreakdownDisplay evaluationData={submissionData.detailedBreakdown} />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default QuestionBreakdownPage;
